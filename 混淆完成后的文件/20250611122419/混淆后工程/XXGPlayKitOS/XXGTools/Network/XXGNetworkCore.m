//
//  XXGNetworkCore.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/18.
//

#import "XXGNetworkCore.h"

#define spaRows(obj) __weak typeof(obj) weak##obj = obj;
#define selectJob(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGNetworkCore()

@property (nonatomic,strong) NSURLSession *xxpk_session;

@end

@implementation XXGNetworkCore


+ (instancetype)shared {
    static XXGNetworkCore *shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.xxpk_session = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.xxpk_session.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)xxpk_sendBaseRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * responseObject))success
                     failure:(void(^)(NSError *error))failure
                  retryCount:(NSInteger)retryCount {

    [self xxpk_sendRequest:request
                   process:processBlock
                   success:success
                   failure:failure
                retryCount:retryCount
            currentAttempt:0];
}

// 内部请求方法，支持重试
- (void)xxpk_sendRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * responseObject))success
                 failure:(void(^)(NSError *error))failure
              retryCount:(NSInteger)retryCount
          currentAttempt:(NSInteger)currentAttempt {

    spaRows(self);
    NSURLSessionDataTask *task = [self.xxpk_session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        selectJob(self);
        
        NSError *finalError = [self handleError:error response:response data:data];
        if (finalError) {
            NSLog(@"请求失败 (第 %ld 次): %@", (long)currentAttempt + 1, finalError.localizedDescription);

            // 如果未超过最大重试次数，则继续尝试
            if (currentAttempt < retryCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self xxpk_sendRequest:request process:processBlock success:success failure:failure retryCount:retryCount currentAttempt:currentAttempt + 1];
                });
                return;
            }

            // 最终失败，回调到主线程
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(finalError);
                });
            }
            return;
        }

        // 让外部处理数据
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            NSLog(@"JSON Decode Error: %@", jsonError.localizedDescription);
            if (failure) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    failure(jsonError);
                });
            }
        }
    }];

    [task resume];
}

// 统一错误处理
- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
