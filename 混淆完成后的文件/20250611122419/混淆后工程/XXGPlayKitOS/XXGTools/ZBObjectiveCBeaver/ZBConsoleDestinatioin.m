//
//  ZBConsoleDestinatioin.m
//  Pods-ZBLog_Example
//
//  Created by <PERSON><PERSON> on 2021/3/13.
//

#import "ZBConsoleDestinatioin.h"
#import "XXGPlayKitConfig.h"

@implementation ZBConsoleDestinatioin

- (NSString *)zb_send:(ZBLogLevel)zb_level zb_msg:(NSString *)zb_msg zb_thread:(NSString *)zb_thread zb_file:(NSString *)zb_file zb_function:(NSString *)zb_function zb_line:(NSUInteger)zb_line zb_context:(id)zb_context {
    
    NSString *time = [self formatDate:yearsExpand.xxpk_tools_logger_formatter timeZone:nil];
    
    NSString *color = [self colorForLevel:zb_level];
    
    NSString *line = [NSString stringWithFormat:@"%lu", (unsigned long)zb_line];
    
    NSString *formattedString = [NSString stringWithFormat:yearsExpand.xxpk_tools_logger_format,color,time,zb_msg];
    
#ifdef XXGPLAYKIT_DEBUG
    formattedString = [NSString stringWithFormat:yearsExpand.xxpk_tools_logger_format_file,color,time,zb_function,line,zb_msg];
#endif
    
    printf("%s\n", [formattedString UTF8String]);
    return formattedString;
}

@end
