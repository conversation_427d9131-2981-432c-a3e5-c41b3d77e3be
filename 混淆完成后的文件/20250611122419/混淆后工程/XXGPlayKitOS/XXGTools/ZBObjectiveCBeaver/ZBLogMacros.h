//
//  ZBLogMacros.h
//  Pods
//
//  Created by <PERSON><PERSON> on 2021/3/13.
//

#ifndef TrustCloseMap
#define TrustCloseMap

#import "ZBLog.h"
#import "ZBLogFormatter.h"
#import "XXGPlayKitConfig.h"

/**
 * These are the two macros that all other macros below compile into.
 * These big multiline macros makes all the other macros easier to read.
 **/
#define SlashCity(lvl, fnct, ctx, frmt, ...)   \
        [ZBLog zb_custom : lvl                    \
                 zb_file : __FILE__               \
             zb_function : fnct                   \
                 zb_line : __LINE__               \
              zb_context : ctx                    \
               zb_format : (frmt), ## __VA_ARGS__]

/**
 * Ready to use log macros with no context or tag.
 **/
#define AskUpdate(lvl, fnct, ctx, frmt, ...) \
        do { if((lvl) != 0) SlashCity(lvl, fnct, ctx, frmt, ##__VA_ARGS__); } while(0)

#define VortexThin(frmt, ...)     AskUpdate(ZBLogLevelError,   __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define ViewLabel(frmt, ...)      AskUpdate(ZBLogLevelWarning, __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define EightInfo(frmt, ...)      AskUpdate(ZBLogLevelInfo,    __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define EnterHuman(frmt, ...)     AskUpdate(ZBLogLevelDebug,   __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)
#define BigDustClock(frmt, ...)   AskUpdate(ZBLogLevelVerbose, __PRETTY_FUNCTION__, nil, frmt, ##__VA_ARGS__)

// 字典格式化日志宏
#define PairWayPinDict(msg, dict)     VortexThin(@"%@\n%@", msg, ZBFormatDict(dict))
#define CanonicalDict(msg, dict)      ViewLabel(@"%@\n%@", msg, ZBFormatDict(dict))
#define SlabHelloDict(msg, dict)      EightInfo(@"%@\n%@", msg, ZBFormatDict(dict))
#define HitMileSpaDict(msg, dict)     EnterHuman(@"%@\n%@", msg, ZBFormatDict(dict))
#define LinerRainYouDict(msg, dict)   BigDustClock(@"%@\n%@", msg, ZBFormatDict(dict))

// 网络请求专用日志宏
#define LeastRequest(url, params)     EightInfo(yearsExpand.xxpk_tools_logger_request_format, url, ZBFormatDict(params))
#define BasalResponse(url, response)  EightInfo(yearsExpand.xxpk_tools_logger_response_format, url, ZBFormatDict(response))
#define IconTargetSexWelshStepchild(url, error) VortexThin(yearsExpand.xxpk_tools_logger_network_error_format, url, ZBFormatDict(error))

// 字典格式化函数声明
NSString* ZBFormatDict(id obj);

#endif /* TrustCloseMap */
