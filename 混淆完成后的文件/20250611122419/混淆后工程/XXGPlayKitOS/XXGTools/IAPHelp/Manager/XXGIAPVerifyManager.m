//
//  XXGIAPVerifyManager.m
//  XXGIAPHelp
//
//  Created by kane on 2018/8/13.
//  Copyright © 2018年 kane. All rights reserved.
//

#import "XXGIAPVerifyManager.h"
#import "XXGIAPTransactionModel.h"
#import "XXGIAPConfig.h"
#import <StoreKit/StoreKit.h>
#import "UICKeyChainStore.h"
#import "XXGPlayKitConfig.h"

@interface XXGIAPVerifyManager ()
{
    XXGIAPTransactionModel *_currentModel;
    NSMutableArray *_modelArray;
    NSString *_keychain_service;
    NSString *_keychain_account;
}

@end

@implementation XXGIAPVerifyManager

- (instancetype)initWithKeychainService:(NSString *)keychainService keychainAccount:(NSString *)keychainAccount{

    self = [super init];
  if (self) {
      _keychain_service = keychainService;
      _keychain_account = keychainAccount;
      NSString *app_bundleid = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
      if (!_keychain_account) {
          _keychain_account= [app_bundleid stringByAppendingString:@".account"];
      }
      if (!_keychain_service) {
          _keychain_service =[app_bundleid stringByAppendingString:@".service"];
      }
      _isVerifing = NO;
      _modelArray = [NSMutableArray new];
  }
  return self;
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
/**
 * 添加需要验证的 model.
 */
- (void)appendPaymentTransactionModel:(XXGIAPTransactionModel *)transactionModel{
    // 取出

   NSMutableArray *keychainSet = [self fetchAllPaymentTransactionModel];
    for (XXGIAPTransactionModel *model in keychainSet) {
        if ([model isEqual:transactionModel]) {
            return;
        }
    }
    [keychainSet addObject:transactionModel];

    [self savePaymentTransactionModels:keychainSet];

}

/**
 * ⚠️ 开始支付凭证验证队列(开始验证之前, 必须保证收据不为空).
 */
- (void)startPaymentTransactionVerifingModel:(XXGIAPTransactionModel *)transactionModel{
    // 防止重复验证
    for (XXGIAPTransactionModel *model in _modelArray) {
        if ([model.xxpk_transactionIdentifier isEqualToString:transactionModel.xxpk_transactionIdentifier]) {
            return;
        }
    }

   __block XXGIAPTransactionModel *resultModel= transactionModel;
     NSMutableArray *keychainSet = [self fetchAllPaymentTransactionModel];

    [keychainSet enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(XXGIAPTransactionModel*  _Nonnull model,NSUInteger idx, BOOL * _Nonnull stop) {

        if (transactionModel.xxpk_applicationUsername ) {
            if ([model.xxpk_applicationUsername isEqualToString:transactionModel.xxpk_applicationUsername]) {
                model.xxpk_transactionIdentifier = transactionModel.xxpk_transactionIdentifier;
                model.xxpk_transactionStatus = TransactionStatusAppleSucc;
                if (transactionModel.xxpk_appStoreReceipt) {
                    model.xxpk_appStoreReceipt = transactionModel.xxpk_appStoreReceipt;
                }
                resultModel = model;

                *stop = YES;
            }
        }else if ([transactionModel.xxpk_productIdentifier isEqualToString:model.xxpk_productIdentifier]) {
             ///最坏的情况transactionModel只有productIdentifier来对比
                model.xxpk_transactionIdentifier = transactionModel.xxpk_transactionIdentifier;
            transactionModel.xxpk_applicationUsername = model.xxpk_applicationUsername;
            if (transactionModel.xxpk_appStoreReceipt) {
                model.xxpk_appStoreReceipt = transactionModel.xxpk_appStoreReceipt;
            }
                model.xxpk_transactionStatus = TransactionStatusAppleSucc;
                  resultModel = model;
                *stop = YES;
            }


    }];

        // 保存更改
        [self savePaymentTransactionModels:keychainSet];

        [_modelArray addObject:resultModel];
        // 开始验证
        [self verifingModel:resultModel];



}
-(void)updatePaymentTransactionCheckCount:(XXGIAPTransactionModel *)transactionModel{

      NSMutableArray *keychainSet = [self fetchAllPaymentTransactionModel];
    [keychainSet enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(XXGIAPTransactionModel*  _Nonnull model,NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model isEqual:transactionModel]) {
            model.xxpk_cancelStatusCheckCount= transactionModel.xxpk_cancelStatusCheckCount;
            *stop = YES;
        }
    }];
    [self savePaymentTransactionModels:keychainSet];
}
-(void)updatePaymentTransactionModelStatus:(XXGIAPTransactionModel *)transactionModel{

      NSMutableArray *keychainSet = [self fetchAllPaymentTransactionModel];
    [keychainSet enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(XXGIAPTransactionModel*  _Nonnull model,NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model isEqual:transactionModel]) {
            model.xxpk_transactionStatus= transactionModel.xxpk_transactionStatus;
            if (transactionModel.xxpk_error) {
                model.xxpk_error = transactionModel.xxpk_error;
            }
            *stop = YES;
        }
    }];
    [self savePaymentTransactionModels:keychainSet];
}

- (void)finishPaymentTransactionVerifingModel:(XXGIAPTransactionModel *)transactionModel{
    for (XXGIAPTransactionModel *model in _modelArray) {
        if ([model.xxpk_transactionIdentifier isEqualToString:transactionModel.xxpk_transactionIdentifier]) {
            [_modelArray removeObject:model];
            break;
        }
    }
       self.isVerifing = NO;
}

/**
 * 删除失败 model.
 */
- (void)deletePaymentTransactionModel:(XXGIAPTransactionModel *)transactionModel{
    NSMutableArray *keychainSet =[self fetchAllPaymentTransactionModel];

    NSInteger count = keychainSet.count;
    [keychainSet enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(XXGIAPTransactionModel*  _Nonnull model,NSUInteger idx, BOOL * _Nonnull stop) {
        if ([model isEqual:transactionModel]) {
            [keychainSet removeObject:model];
               NSLog(yearsExpand.xxpk_log_iap_delete_order_success,transactionModel);
        }
    }];

    if (count == keychainSet.count) {
         NSLog(yearsExpand.xxpk_log_iap_delete_order_failed,transactionModel);
    }
    [self savePaymentTransactionModels:keychainSet];
}

- (void)verifingModel:(XXGIAPTransactionModel *)transactionModel{

    if (_isVerifing) {
        NSLog(yearsExpand.xxpk_log_iap_verifying,_currentModel);
        return;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(startPaymentTransactionVerifingModel:)]) {
        _isVerifing = YES;
        _currentModel = transactionModel;
         NSLog(yearsExpand.xxpk_log_iap_start_verify,_currentModel);
        [self.delegate startPaymentTransactionVerifingModel:transactionModel];
    }
}

/**
 获取所有交易模型

 @return model
 */
- (NSMutableArray <XXGIAPTransactionModel *>*)fetchAllPaymentTransactionModel{

    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:_keychain_service];
    NSData *keychainData = [keychain dataForKey:_keychain_account];
    NSMutableArray *mutableArray =[NSMutableArray new];
    if (keychainData) {
        NSError *error;
        id object = [NSJSONSerialization JSONObjectWithData:keychainData
                                                   options:kNilOptions
                                                     error:&error];
        if (![object isKindOfClass:[NSArray class]] || error) {
            NSLog(yearsExpand.xxpk_log_iap_error, error.localizedDescription);
            return mutableArray;
        }

        for (NSDictionary *dic in (NSArray *)object) {

            XXGIAPTransactionModel *model = [XXGIAPTransactionModel xxpk_modelWithDic:dic];
            [mutableArray addObject:model];
        }
    }
    return mutableArray;
}


- (void)savePaymentTransactionModels:(NSArray <XXGIAPTransactionModel *>*)models{

    NSMutableArray *mutableArray =[NSMutableArray new];
    for (XXGIAPTransactionModel *model in models) {
        NSDictionary *dic = [model xxpk_toDic];
        [mutableArray addObject:dic];
    }
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:mutableArray
                                                      options:kNilOptions
                                                        error:&error];
    if (!jsonData) {
        NSLog(yearsExpand.xxpk_log_iap_error, error.localizedDescription);
    }
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:_keychain_service];
    [keychain setData:jsonData forKey:_keychain_account];
}

- (void)cleanAllModels {
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:_keychain_service];
    [keychain removeItemForKey:_keychain_account];
}

@end
