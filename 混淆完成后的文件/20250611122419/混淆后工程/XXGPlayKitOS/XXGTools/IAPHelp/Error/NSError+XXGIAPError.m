//
//  NSError+XXGIAPError.m
//  IPADemo
//
//  Created by <PERSON> on 2019/12/11.
//  Copyright © 2019 kane. All rights reserved.
//

#import "NSError+XXGIAPError.h"
#import "XXGPlayKitConfig.h"

@implementation NSError (XXGIAPError)
+ (instancetype)errorWithXXGIAPCode:(XXGIAPErrorCode)code{
    NSString *msg = @"";
    switch (code) {
        case XXGIAPErrorCodePaying:
            msg  = maleKilometer.xxpk_tool_iap_error_paying;
            break;
        case XXGIAPErrorCodeParameter:
            msg  = maleKilometer.xxpk_tool_iap_error_params;
            break;
        case XXGIAPErrorCodePermission:
            msg  = maleKilometer.xxpk_tool_iap_error_permission;
            break;
        case XXGIAPErrorCodeProductId:
            msg  = maleKilometer.xxpk_tool_iap_error_productcode;
            break;
        case XXGIAPErrorCodeReceipt:
            msg  = maleKilometer.xxpk_tool_iap_error_receipt;
            break;
        case XXGIAPErrorCodeVerifyInvalid:
            msg  = maleKilometer.xxpk_tool_iap_error_verifyinvalid;
            break;
        case XXGIAPErrorCodeNet:
            msg  = maleKilometer.xxpk_tool_iap_error_net;
            break;
        case XXGIAPErrorCodeNotRegistered:
            msg  = maleKilometer.xxpk_tool_iap_error_notregistered;
            break;
        case XXGIAPErrorCodeHasUnfinishedTransaction:
            msg  = maleKilometer.xxpk_tool_iap_error_hasunfinished;
            break;
    }
    NSError *error = [NSError errorWithDomain:yearsExpand.xxpk_tools_iap_domain code:code userInfo:@{NSLocalizedDescriptionKey:msg}];
    return  error;
}
@end
