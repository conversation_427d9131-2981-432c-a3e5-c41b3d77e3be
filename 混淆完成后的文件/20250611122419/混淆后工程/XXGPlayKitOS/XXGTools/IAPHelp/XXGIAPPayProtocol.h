//
//  XXGIAPPayDelegate.h
//  XXGIAPHelp
//
//  Created by kane on 2018/8/7.
//  Copyright © 2018年 kane. All rights reserved.
//


#import <Foundation/Foundation.h>

@class XXGIAPTransactionModel;
@class SKProduct;

typedef enum : NSUInteger {
    XXGIAPVerifyFailed,
    XXGIAPVerifyValid,
    XXGIAPVerifyInvalid,
    XXGIAPVerifyNeedRefreshReceipt
} XXGIAPVerifyResult;

typedef enum : NSUInteger {
    XXGIAPLoadingStatus_None,
    XXGIAPLoadingStatus_CheckingProduct,
    XXGIAPLoadingStatus_Paying,
    XXGIAPLoadingStatus_Restoring,
    XXGIAPLoadingStatus_Verifying,
} XXGIAPLoadingStatus;


typedef void(^VerifyRsultBlock)(XXGIAPVerifyResult result);

@protocol XXGIAPPayDelegate <NSObject>

/**
 票据验证方法

 @param model 交易模型
 @param resultAction ⚠️需要执行的block(将从自己服务器验证的结果传入执行)
 */
- (void)verifyWithModel:(XXGIAPTransactionModel *)model resultAction:(VerifyRsultBlock)resultAction;

@optional

/// 当前的状态
/// @param status 状态
- (void)currentStatus:(XXGIAPLoadingStatus)status;

/**
 获取苹果商店信息

 @param products 商店物品
 @param error 错误信息
 */
-(void)onLaunProductListFinish:(SKProduct *)products withError:(NSError*)error;


/**
 苹果支付成功回调

 @param model 交易模型
 */
-(void)onIAPPaymentSucess:(XXGIAPTransactionModel*)model;


/**
 苹果支付失败回调

 @param model 交易模型
 @param error 错误信息
 (如果是XXGIAPErrorCodeHasUnfinishedTransaction,需要调用
 [[XXGIAPHelpManager sharedManager] checkUnfinishTransaction]]
 来触发补单流程
 )
 */
-(void)onIAPPayFailue:(XXGIAPTransactionModel*)model  withError:(NSError*)error;


/**
 苹果恢复购买结果
 
 @param productIdentifiers 恢复购买结果
 @param error 错误信息
 */
-(void)onIAPRestoreResult:(NSArray*)productIdentifiers  withError:(NSError*)error;

/**
 发货成功回调

 @param model 交易模型
 */
-(void)onDistributeGoodsFinish:(XXGIAPTransactionModel*)model;

// 发货失败回调
-(void)onDistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error;


/*******************补发回调用********************/

// 补发货成功回调
-(void)onRedistributeGoodsFinish:(XXGIAPTransactionModel*)model;

// 补发货失败回调
-(void)onRedistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error;

/***************************************/



/**
 输出日志

 @param log 日志信息
 */
- (void)GetAtomic:(NSString *)log;
@end

