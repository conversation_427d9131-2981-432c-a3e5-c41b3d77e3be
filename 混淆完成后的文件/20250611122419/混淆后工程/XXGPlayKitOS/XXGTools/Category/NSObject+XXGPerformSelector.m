//
//  NSObject+XXGPerformSelector.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/21.
//

#import "NSObject+XXGPerformSelector.h"
#import <UIKit/UIKit.h>
#import "ZBObjectiveCBeaver.h"
#import "XXGPlayKitConfig.h"

@implementation NSObject (XXGPerformSelector)

- (id)xxpk_performSelector:(SEL)aSelector {
    return [self xxpk_performSelector:aSelector withObjects:@[]];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1{
    // 将参数包装为数组，允许 nil 并转换为 NSNull
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2 {
    // 将参数包装为数组，允许 nil 并转换为 NSNull
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];
    if (object2) [objects addObject:object2]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3 {
    // 将参数包装为数组，允许 nil 并转换为 NSNull
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];
    if (object2) [objects addObject:object2]; else [objects addObject:[NSNull null]];
    if (object3) [objects addObject:object3]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4 {
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];
    if (object2) [objects addObject:object2]; else [objects addObject:[NSNull null]];
    if (object3) [objects addObject:object3]; else [objects addObject:[NSNull null]];
    if (object4) [objects addObject:object4]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5 {
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];
    if (object2) [objects addObject:object2]; else [objects addObject:[NSNull null]];
    if (object3) [objects addObject:object3]; else [objects addObject:[NSNull null]];
    if (object4) [objects addObject:object4]; else [objects addObject:[NSNull null]];
    if (object5) [objects addObject:object5]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5
                withObject:(id)object6 {
    NSMutableArray *objects = [NSMutableArray array];
    if (object1) [objects addObject:object1]; else [objects addObject:[NSNull null]];
    if (object2) [objects addObject:object2]; else [objects addObject:[NSNull null]];
    if (object3) [objects addObject:object3]; else [objects addObject:[NSNull null]];
    if (object4) [objects addObject:object4]; else [objects addObject:[NSNull null]];
    if (object5) [objects addObject:object5]; else [objects addObject:[NSNull null]];
    if (object6) [objects addObject:object6]; else [objects addObject:[NSNull null]];

    return [self xxpk_performSelector:aSelector withObjects:objects];
}

- (id)xxpk_performSelector:(SEL)selector withObjects:(NSArray *)objects {
    EightInfo(yearsExpand.xxpk_log_perform_selector,NSStringFromClass([self class]), NSStringFromSelector(selector));

    // 1. 获取方法签名
    NSMethodSignature *signature = [self methodSignatureForSelector:selector];
    if (!signature) {
        VortexThin(yearsExpand.xxpk_log_perform_instance_not_found);
        signature = [[self class] instanceMethodSignatureForSelector:selector];
        if (!signature) {
            VortexThin(yearsExpand.xxpk_log_perform_class_not_found);
            return nil;
        }
    }

    // 2. 校验参数数量
    NSUInteger expectedArgCount = signature.numberOfArguments - 2; // 跳过 self 和 _cmd
    if (objects.count != expectedArgCount) {
        VortexThin(yearsExpand.xxpk_log_perform_param_mismatch, (unsigned long)expectedArgCount, (unsigned long)objects.count);
        return nil;
    }

    // 3. 创建并配置 NSInvocation
    NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
    invocation.target = self;
    invocation.selector = selector;

    // 4. 设置参数（支持 NSNull 占位符转 nil）
    [objects enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        id argument = [obj isKindOfClass:[NSNull class]] ? nil : obj;
        [invocation setArgument:&argument atIndex:idx + 2]; // 参数从索引 2 开始
    }];

    // 5. 调用方法
    [invocation invoke];

    // 6. 正确获取返回值（与原始实现一致）
    if (signature.methodReturnLength == 0) return nil;

    const char *returnType = signature.methodReturnType;
    id returnValue = nil;

    if (strcmp(returnType, @encode(id)) == 0 || strcmp(returnType, @encode(Class)) == 0) {
        __unsafe_unretained id tempResult = nil;
        [invocation getReturnValue:&tempResult];
        returnValue = tempResult;
    } else if (strcmp(returnType, @encode(BOOL)) == 0) {
        BOOL result;
        [invocation getReturnValue:&result];
        returnValue = @(result);
    } else if (strcmp(returnType, @encode(int)) == 0) {
        int result;
        [invocation getReturnValue:&result];
        returnValue = @(result);
    } else if (strcmp(returnType, @encode(CGRect)) == 0) {
        CGRect rect;
        [invocation getReturnValue:&rect];
        returnValue = [NSValue valueWithCGRect:rect];
    } else {
        void *buffer = malloc(signature.methodReturnLength);
        [invocation getReturnValue:buffer];
        returnValue = [NSValue valueWithBytes:buffer objCType:returnType];
        free(buffer);
    }

    return returnValue;
}

@end
