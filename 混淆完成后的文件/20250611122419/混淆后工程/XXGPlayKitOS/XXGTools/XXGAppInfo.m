//
//  XXGAppInfo.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/25.
//

#import "XXGAppInfo.h"
#import "XXGPlayKitConfig.h"
#import "ZBObjectiveCBeaver.h"

@import AdSupport;
@import AppTrackingTransparency;
@import UIKit;

#import "sys/utsname.h" //utsname

@implementation XXGAppInfo

+ (UIImage *)xxpk_getAppIconImage {
    NSDictionary *infoPlist = [[NSBundle mainBundle] infoDictionary];
    NSString *icon = [[infoPlist valueForKeyPath:@"CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles"] lastObject];
    return [UIImage imageNamed:icon];
}

+ (NSString *)xxpk_appBundleIdentifier {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
}

+ (NSString *)xxpk_appVersion {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)xxpk_appName {
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    return displayName;
}

+ (NSString *)xxpk_deviceName {
    return [UIDevice currentDevice].name;
}

+ (NSString *)xxpk_deviceIdfa {
    return [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
}

+ (NSString *)xxpk_deviceIdfv {
    return [UIDevice currentDevice].identifierForVendor.UUIDString;
}

+ (NSString *)xxpk_deviceModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

+ (NSString *)xxpk_systemVersion {
    return [UIDevice currentDevice].systemVersion;
}

+ (NSString *)xxpk_applicationPath {
    return NSHomeDirectory().lastPathComponent;
}

+ (BOOL)xxpk_laiguo {
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    return [userDefault boolForKey:yearsExpand.xxpk_tools_laiguo];
}
+ (void)setXxpk_laiguo:(BOOL)xxpk_laiguo {
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    [userDefault setBool:xxpk_laiguo forKey:yearsExpand.xxpk_tools_laiguo];
    [userDefault synchronize];
}

+ (void)xxpk_requestIDFAIfNeeded:(void (^)(void))complate {
    static dispatch_once_t onceToken;
    static BOOL isProcessing = NO;

    // 防止重复调用
    if (isProcessing) {
        EightInfo(yearsExpand.xxpk_log_att_duplicate_request);
        return;
    }

    dispatch_once(&onceToken, ^{
        isProcessing = YES;
        EightInfo(yearsExpand.xxpk_log_att_start_check);

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];

            NSString *statusDesc = [self __stringForATTStatus:status];

            EightInfo(yearsExpand.xxpk_log_att_current_status, statusDesc, (long)status);

            switch (status) {
                case ATTrackingManagerAuthorizationStatusAuthorized:
                    EightInfo(yearsExpand.xxpk_log_att_authorized_direct);
                    isProcessing = NO;
                    if (complate) {
                        complate();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusDenied:
                    EightInfo(yearsExpand.xxpk_log_att_denied);
                    isProcessing = NO;
                    if (complate) {
                        complate();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusRestricted:
                    EightInfo(yearsExpand.xxpk_log_att_restricted);
                    isProcessing = NO;
                    if (complate) {
                        complate();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusNotDetermined:
                    EightInfo(yearsExpand.xxpk_log_att_not_determined);
                    [self __waitForAppActiveAndRequestPermission:^{
                        isProcessing = NO;
                        if (complate) {
                            complate();
                        }
                    }];
                    break;
            }
        } else {
            EightInfo(yearsExpand.xxpk_log_att_ios_below_14);
            isProcessing = NO;
            if (complate) {
                complate();
            }
        }
    });
}

+ (void)__waitForAppActiveAndRequestPermission:(void (^)(void))completion {
    EightInfo(yearsExpand.xxpk_log_att_wait_app_active);

    // 延迟几秒后请求权限
    static int delaySeconds = 6;

    __block id observer = [[NSNotificationCenter defaultCenter]
        addObserverForName:UIApplicationDidBecomeActiveNotification
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {

        EightInfo(yearsExpand.xxpk_log_att_app_active_delay, delaySeconds);

        // 延迟6秒后触发授权请求，给用户适应时间
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delaySeconds * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{


            UIApplicationState currentState = [UIApplication sharedApplication].applicationState;

            NSString *stateDesc = [self __stringForAppState:currentState];

            EightInfo(yearsExpand.xxpk_log_att_delay_app_state, stateDesc);

            if (currentState == UIApplicationStateActive) {
                EightInfo(yearsExpand.xxpk_log_att_app_active_request);
                [self __performATTRequest:completion];
            } else {

                EightInfo(yearsExpand.xxpk_log_att_app_inactive, stateDesc);
                EightInfo(yearsExpand.xxpk_log_att_add_observer2);
                observer = [[NSNotificationCenter defaultCenter]
                    addObserverForName:UIApplicationDidBecomeActiveNotification
                                object:nil
                                 queue:[NSOperationQueue mainQueue]
                            usingBlock:^(NSNotification *notification) {
                    // 移除观察者，确保只触发一次
                    EightInfo(yearsExpand.xxpk_log_att_remove_observer2);
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    EightInfo(yearsExpand.xxpk_log_att_app_active_direct);
                    [self __performATTRequest:completion];
                }];
            }

        });

        EightInfo(yearsExpand.xxpk_log_att_remove_observer);
        // 移除观察者，确保只触发一次
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
    }];
}

+ (void)__performATTRequest:(void (^)(void))completion {
    if (@available(iOS 14, *)) {
        EightInfo(yearsExpand.xxpk_log_att_showing_dialog);

        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];

            NSString *callbackStatusDesc = [self __stringForATTStatus:status];
            NSString *currentStatusDesc = [self __stringForATTStatus:currentStatus];

            EightInfo(yearsExpand.xxpk_log_att_request_complete);
            EightInfo(yearsExpand.xxpk_log_att_callback_status, callbackStatusDesc, (long)status);
            EightInfo(yearsExpand.xxpk_log_att_current_actual_status, currentStatusDesc, (long)currentStatus);

            // 使用实际状态判断，解决iOS 17.4+的回调状态不准确问题
            // 这里17.4以后在弹窗未点击时就已经先回调一次这个函数并且
            // [ATTrackingManager trackingAuthorizationStatus]获取为0，回调中的status为2 所以 [ATTrackingManager trackingAuthorizationStatus] 以此变量为准
            // 回调中status不准 此处为iOS17.4 bug
            BOOL isAuthorized = (currentStatus == ATTrackingManagerAuthorizationStatusAuthorized) ||
                               (status == ATTrackingManagerAuthorizationStatusAuthorized);

            if (isAuthorized) {
                EightInfo(yearsExpand.xxpk_log_att_authorized_success);
                if (completion) {
                    completion();
                }
            } else if (currentStatus == ATTrackingManagerAuthorizationStatusNotDetermined) {
                EightInfo(yearsExpand.xxpk_log_att_still_not_determined);
                [self __waitingForUserOperationAuthorization:completion currentAttempt:0];
            } else {
                EightInfo(yearsExpand.xxpk_log_att_denied_restricted);
                if (completion) {
                    completion();
                }
            }
        }];
    }
}

// 添加状态描述辅助方法
+ (NSString *)__stringForATTStatus:(ATTrackingManagerAuthorizationStatus)status  API_AVAILABLE(ios(14)){
    if (@available(iOS 14, *)) {
        switch (status) {
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return yearsExpand.xxpk_att_status_not_determined;
            case ATTrackingManagerAuthorizationStatusRestricted:
                return yearsExpand.xxpk_att_status_restricted;
            case ATTrackingManagerAuthorizationStatusDenied:
                return yearsExpand.xxpk_att_status_denied;
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return yearsExpand.xxpk_att_status_authorized;
            default:
                return [NSString stringWithFormat:yearsExpand.xxpk_att_status_unknown, (long)status];
        }
    }
    return yearsExpand.xxpk_att_status_ios_not_support;
}

+ (NSString *)__stringForAppState:(UIApplicationState)state {
    switch (state) {
        case UIApplicationStateActive:
            return yearsExpand.xxpk_app_state_active;
        case UIApplicationStateInactive:
            return yearsExpand.xxpk_app_state_inactive;
        case UIApplicationStateBackground:
            return yearsExpand.xxpk_app_state_background;
        default:
            return [NSString stringWithFormat:yearsExpand.xxpk_app_state_unknown, (long)state];
    }
}

// 等待用户操作授权
+ (void)__waitingForUserOperationAuthorization:(void (^)(void))complate currentAttempt:(NSInteger)currentAttempt {
    NSInteger retryCount = 10;

    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];

        NSString *statusDesc = [self __stringForATTStatus:currentStatus];

        EightInfo(yearsExpand.xxpk_log_att_waiting_user,
              (long)(currentAttempt + 1), (long)retryCount, statusDesc);

        // 如果未超过最大重试次数且状态仍未确定，则继续等待
        if (currentStatus == ATTrackingManagerAuthorizationStatusNotDetermined && currentAttempt < retryCount) {
            EightInfo(yearsExpand.xxpk_log_att_still_waiting, (long)(currentAttempt + 2));

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)),
                          dispatch_get_main_queue(), ^{
                [self __waitingForUserOperationAuthorization:complate currentAttempt:currentAttempt + 1];
            });
            return;
        } else {
            
            // 结束等待的情况
            if (currentAttempt >= retryCount) {
                EightInfo(yearsExpand.xxpk_log_att_timeout, (long)retryCount);
                EightInfo(yearsExpand.xxpk_log_att_timeout_final, statusDesc);
            } else {
                EightInfo(yearsExpand.xxpk_log_att_user_choice, statusDesc);

                if (currentStatus == ATTrackingManagerAuthorizationStatusAuthorized) {
                    EightInfo(yearsExpand.xxpk_log_att_final_authorized);
                } else if (currentStatus == ATTrackingManagerAuthorizationStatusDenied) {
                    EightInfo(yearsExpand.xxpk_log_att_final_denied);
                } else if (currentStatus == ATTrackingManagerAuthorizationStatusRestricted) {
                    EightInfo(yearsExpand.xxpk_log_att_final_restricted);
                }
            }

            EightInfo(yearsExpand.xxpk_log_att_wait_end);
            if (complate) {
                complate();
            }
        }
    } else {
        EightInfo(yearsExpand.xxpk_log_att_ios_below_14_wait);
        if (complate) {
            complate();
        }
    }
}
@end
