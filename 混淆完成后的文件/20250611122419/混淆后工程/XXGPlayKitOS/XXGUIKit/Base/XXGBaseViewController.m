//
//  XXGBaseViewController.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/7.
//

#import "XXGBaseViewController.h"

@interface XXGBaseViewController ()

@end

@implementation XXGBaseViewController

- (UIButton *)xxpk_backButton
{
    if (!_xxpk_backButton) {
        _xxpk_backButton = [[UIButton alloc] init];
        [_xxpk_backButton setTitle:XXGUIDriver.xxpk_data_ui.xxpk_ui_base_btn_back forState:UIControlStateNormal];
        [_xxpk_backButton setTitleColor:[XXGUIDriver xxpk_textColor] forState:UIControlStateNormal];
        [_xxpk_backButton addTarget:self action:@selector(xxpk_backButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _xxpk_backButton;
}

- (UIButton *)xxpk_closeButton
{
    if (!_xxpk_closeButton) {
        _xxpk_closeButton = [[UIButton alloc] init];
        [_xxpk_closeButton setTitle:XXGUIDriver.xxpk_data_ui.xxpk_ui_base_btn_close forState:UIControlStateNormal];
        [_xxpk_closeButton setTitleColor:[XXGUIDriver xxpk_textColor] forState:UIControlStateNormal];
        [_xxpk_closeButton addTarget:self action:@selector(xxpk_closeButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        _xxpk_closeButton.hidden = [XXGUIDriver xxpk_closeButtonHidden];
    }
    return _xxpk_closeButton;
}

- (void)xxpk_backButtonAction:(UIButton *)sender {
    if(self.navigationController.viewControllers.count > 1) {
        [self.view endEditing:YES];
        [self.navigationController popViewControllerAnimated:NO];
    }else {
        [self xxpk_closeButtonAction:sender];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)xxpk_closeButtonAction:(UIButton *)sender {
    [[XXGWindowManager shared] xxpk_dismissWindow];
    [XXGUIDriver xxpk_closeButtonAction];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // Do any additional setup after loading the view.
    self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    self.view.layer.cornerRadius = 2;
    self.view.backgroundColor = [XXGUIDriver xxpk_backgroundColor];
    [self.view addSubview:self.xxpk_backButton];
    [self.view addSubview:self.xxpk_closeButton];
    
    CGFloat btSize = XXGUIDriver.xxpk_data_ui.xxpk_float30;
    [_xxpk_backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.size.mas_equalTo(CGSizeMake(btSize, btSize));
    }];
    [_xxpk_closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.right.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.size.mas_equalTo(CGSizeMake(btSize, btSize));
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(xxpk_keyboardWillChangeFrame:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(xxpk_keyboardDidHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo([XXGUIDriver xxpk_mainContentViewSize]);
    }];
}

// MARK: - 键盘弹出
- (void)xxpk_keyboardWillChangeFrame:(NSNotification *)notification {
    // 取出键盘动画的时间(根据userInfo的key----UIKeyboardAnimationDurationUserInfoKey)
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    // 取得键盘最后的frame(根据userInfo的key----UIKeyboardFrameEndUserInfoKey = "NSRect: {{0, 227}, {320, 253}}";)
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    UIWindow *keyWindow = [XXGWindowManager shared].xxpk_currentWindow;
    if (![keyWindow isMemberOfClass:NSClassFromString(XXGUIDriver.xxpk_data_ui.xxpk_ui_base_keyboardShimWin)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        UIView *firstResponder = [keyWindow performSelector:@selector(firstResponder)];
#pragma clang diagnostic pop
        
        if (firstResponder  && [firstResponder isKindOfClass:UITextField.class]) {

            CGRect tfRect = [keyWindow convertRect:firstResponder.frame fromView:firstResponder.superview];
            
            if ((tfRect.origin.y + tfRect.size.height) > keyboardFrame.origin.y) {
                CGFloat trans = ((tfRect.origin.y + tfRect.size.height) - keyboardFrame.origin.y) + 10;
                // 执行动画
                spaRows(self);
                [UIView animateWithDuration:duration animations:^{
                    selectJob(self);
                    self.navigationController.view.transform = CGAffineTransformMakeTranslation(0, -trans);
                }];
            }
        }
    }
}

// MARK: - 键盘收回
- (void)xxpk_keyboardDidHide:(NSNotification *)notification{
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    spaRows(self);
    [UIView animateWithDuration:duration animations:^{
        selectJob(self);
        self.navigationController.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    [self.view endEditing:YES];
}

- (void)xxpk_touchesBlank:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

- (void)dealloc {
    NSLog(@"dealloc - %@",NSStringFromClass([self class]));
    [self.view endEditing:YES];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

@end
