//
//  XXGComeinViewController.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/7.
//

#import "XXGComeinViewController.h"
#import "XXGMobileViewController.h"
#import "XXGAccountViewController.h"
#import "XXGServiceViewController.h"
#import "XXGProtocolLabel.h"
#import "XXGContentTextViewController.h"

@interface XXGComeinViewController ()
@property (nonatomic, strong) NSArray *xxpk_btns;
@property (nonatomic,strong) XXGProtocolLabel *xxpk_protoclLabel;
@end

@implementation XXGComeinViewController

- (NSArray *)xxpk_btns {
    if (!_xxpk_btns) {
        _xxpk_btns =  [XXGUIDriver xxpk_comeinBtnsWithTarget:self action:@selector(xxpk_comeinBtnDidClick:)];
    }
    return _xxpk_btns;
}

- (XXGProtocolLabel *)xxpk_protoclLabel {
    if (!_xxpk_protoclLabel) {
        _xxpk_protoclLabel = [XXGProtocolLabel xxpk_protocolLabel];
    }
    return _xxpk_protoclLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self xxpk_addSubviews];
}

- (void)xxpk_addSubviews {
    UIView *xxpk_logoView = [XXGUIDriver xxpk_logoView];
    [self.view addSubview:xxpk_logoView];
    [xxpk_logoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float60);
        make.left.equalTo(self.xxpk_backButton.mas_right);
        make.right.equalTo(self.xxpk_closeButton.mas_left);
    }];
    
    CGFloat stackWidth = [XXGUIDriver xxpk_mainContentViewSize].width - XXGUIDriver.xxpk_data_ui.xxpk_float20;
    CGFloat spacing = 0;
    CGFloat btWith = stackWidth / self.xxpk_btns.count;
    
    if (btWith > XXGUIDriver.xxpk_data_ui.xxpk_float57) {
        spacing = (stackWidth - XXGUIDriver.xxpk_data_ui.xxpk_float57*self.xxpk_btns.count)/(self.xxpk_btns.count-1)/2;
    }
    
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.distribution = UIStackViewDistributionEqualCentering;
    stackView.spacing = spacing;
    [self.view addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_logoView.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.centerX.equalTo(self.view); // 核心修改点：改为水平居中
        if (btWith < XXGUIDriver.xxpk_data_ui.xxpk_float57) {
            make.width.mas_equalTo(stackWidth);
        }
    }];
    
    // 3. 将按钮添加到 StackView 并设置固定宽高
    [self.xxpk_btns enumerateObjectsUsingBlock:^(UIView *view, NSUInteger idx, BOOL * _Nonnull stop) {
        [stackView addArrangedSubview:view]; // 关键：使用 addArrangedSubview
        
        // 设置按钮宽高约束
        [view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(MIN(btWith,XXGUIDriver.xxpk_data_ui.xxpk_float57));
        }];
    }];
    
    // 联系客服
    UIButton *xxpk_servicebutton = [XXGUIDriver xxpk_buttonNormal:XXGUIDriver.xxpk_string_ui.xxpk_customerService];
    [xxpk_servicebutton addTarget:self action:@selector(xxpk_serviceBtnAction:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_servicebutton];
    [xxpk_servicebutton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-8);
        make.height.mas_equalTo(16);
        make.centerX.equalTo(self.view);
    }];
    
    [self.view addSubview:self.xxpk_protoclLabel];
    [self.xxpk_protoclLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(xxpk_servicebutton.mas_top).offset(-8);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float20);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float20);
    }];
    
    spaRows(self);
    self.xxpk_protoclLabel.xxpk_handleProtocolTap = ^{
        selectJob(self);
        [self xxpk_handleProtocolTap];
    };
}

- (void)xxpk_comeinBtnDidClick:(UIButton *)button {
    
    if (!self.xxpk_protoclLabel.xxpk_isChecked) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:[XXGUIDriver.xxpk_string_ui.xxpk_readProtocl stringByAppendingString:XXGUIDriver.xxpk_string_ui.xxpk_protoclon] buttonTitles:@[XXGUIDriver.xxpk_string_ui.xxpk_agree, XXGUIDriver.xxpk_string_ui.xxpk_noagree] completion:^(NSInteger buttonIndex) {
            if (buttonIndex == 0) {
                self.xxpk_protoclLabel.xxpk_isChecked = YES;
            }
        }];
        return;
    }
    
    NSDictionary<NSString *, NSString *> *map;
    map = @{
        // 基础动作类型
        XXGUIDriver.xxpk_data_ui.xxpk_guest        : XXGUIDriver.xxpk_data_ui.xxpk_func_guestBtnDidClick,
        XXGUIDriver.xxpk_data_ui.xxpk_mobile       : XXGUIDriver.xxpk_data_ui.xxpk_func_mobileBtnDidClick,
        XXGUIDriver.xxpk_data_ui.xxpk_register     : XXGUIDriver.xxpk_data_ui.xxpk_func_registerBtnDidClick,

XXGUIDriver.xxpk_data_ui.xxpk_vk           : XXGUIDriver.xxpk_data_ui.xxpk_func_vkBtnDidClick,
        XXGUIDriver.xxpk_data_ui.xxpk_facebook     : XXGUIDriver.xxpk_data_ui.xxpk_func_facebookBtnDidClick,
        XXGUIDriver.xxpk_data_ui.xxpk_poopo        : XXGUIDriver.xxpk_data_ui.xxpk_func_poopoBtnDidClick
    };
    
    // 安全访问并转换类型
    NSString *selStr = map[button.accessibilityIdentifier];
    SEL sel = NSSelectorFromString(selStr);
    if ([self respondsToSelector:sel]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
        [self performSelector:sel withObject:button];
#pragma clang diagnostic pop
    }
}
- (void)xxpk_guestBtnDidClick:(UIButton *)button {
    NSLog(@"%s",__func__);
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_gusetButtonDidClick:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_gusetButtonDidClick:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}
- (void)xxpk_mobileBtnDidClick:(UIButton *)button {
    XXGMobileViewController *vc = [XXGMobileViewController new];
    vc.xxpk_delegate = self.xxpk_delegate;
    [self.navigationController pushViewController:vc animated:NO];
    NSLog(@"%s",__func__);
}
- (void)xxpk_registerBtnDidClick:(UIButton *)button {
    XXGAccountViewController *vc = [XXGAccountViewController new];
    vc.xxpk_delegate = self.xxpk_delegate;
    [self.navigationController pushViewController:vc animated:NO];
    NSLog(@"%s",__func__);
}

- (void)xxpk_vkBtnDidClick:(UIButton *)button {
    NSLog(@"%s",__func__);
    if (self.xxpk_delegate && [self.xxpk_delegate respondsToSelector:@selector(xxpk_VKButtonDidClick:)]) {
        [XXGLoadingView showLoadingOnView:self.view];
        [self.xxpk_delegate xxpk_VKButtonDidClick:^(id object) {
            [XXGLoadingView hideLoadingFromView:self.view];
        }];
    }
}
- (void)xxpk_facebookBtnDidClick:(UIButton *)button {
    NSLog(@"%s",__func__);
    if (self.xxpk_delegate && [self.xxpk_delegate respondsToSelector:@selector(xxpk_facebookButtondDidClick:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_facebookButtondDidClick:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}
- (void)xxpk_poopoBtnDidClick:(UIButton *)button {
    NSLog(@"%s",__func__);
    if (self.xxpk_delegate && [self.xxpk_delegate respondsToSelector:@selector(xxpk_poopoButtonDidClick:)]) {
        [self.xxpk_delegate xxpk_poopoButtonDidClick:nil];
    }
}

- (void)xxpk_serviceBtnAction:(UIButton *)button {
    NSLog(@"%s",__func__);
    XXGServiceViewController *vc = [XXGServiceViewController new];
    vc.xxpk_delegate = self.xxpk_delegate;
    [self.navigationController pushViewController:vc animated:NO];
}

- (void)xxpk_handleProtocolTap {
    NSLog(@"%s",__func__);
    XXGContentTextViewController *agreement_vc = [XXGContentTextViewController new];
    [agreement_vc setXxpk_completion:^(BOOL result) {
        self.xxpk_protoclLabel.xxpk_isChecked = result;
    }];
    [self.navigationController pushViewController:agreement_vc animated:NO];
}
@end
