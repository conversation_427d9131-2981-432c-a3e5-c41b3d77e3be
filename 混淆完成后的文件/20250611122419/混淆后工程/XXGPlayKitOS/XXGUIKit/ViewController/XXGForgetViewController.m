//
//  XXGForgetViewController.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/10.
//

#import "XXGForgetViewController.h"
#import "XXGSendCodeButton.h"
#import "XXGToast.h"
#import "XXGMobileTextField.h"
#import "NSString+XXGString.h"

@interface XXGForgetViewController ()

@property (nonatomic, strong) XXGMobileTextField *xxpk_mobileTextField;
@property (nonatomic, strong) UITextField *xxpk_codeTextField;
@property (nonatomic, strong) UITextField *xxpk_passwordTextField;
@property (nonatomic, strong) XXGSendCodeButton *xxpk_codeButton;
@end

@implementation XXGForgetViewController

- (XXGSendCodeButton *)xxpk_codeButton {
    if (!_xxpk_codeButton) {
        _xxpk_codeButton = [[XXGSendCodeButton alloc] init];
    }
    return _xxpk_codeButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // xx手机号
    self.xxpk_mobileTextField = [[XXGMobileTextField alloc] initWithController:self];
    [self.view addSubview:self.xxpk_mobileTextField];
    [self.xxpk_mobileTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float45);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    // 验证码xx
    self.xxpk_codeTextField = [XXGUIDriver xxpk_textFieldOfVerificationCode];
    [self.view addSubview:self.xxpk_codeTextField];
    [self.xxpk_codeTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_mobileTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    spaRows(self);
    self.xxpk_codeButton.xxpk_sendCodeAction = ^{
        selectJob(self);
        // 此处实现验证码发送请求
        NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
        NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
        if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
            [self.xxpk_codeButton xxpk_stopCountdown];
            [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
            return;
        }
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_sendCodeButtonDidClickWithType:xxpk_moblil:dialCode:completion:)]) {
            [XXGLoadingView showLoadingOnWindow];
            [self.xxpk_delegate xxpk_sendCodeButtonDidClickWithType:XXGUIDriver.xxpk_data_ui.xxpk_code_forget xxpk_moblil:xxpk_mobile dialCode:xxpk_dialCode completion:^(id object) {
                [XXGLoadingView hideLoadingFromWindow];
                if ([object boolValue]) {
                    [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_sendedVerificationCode];
                }else {
                    [self.xxpk_codeButton xxpk_stopCountdown];
                }
            }];
        }
    };
    [self.view addSubview:self.xxpk_codeButton];
    [self.xxpk_codeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.xxpk_codeTextField);
        make.height.equalTo(self.xxpk_codeTextField);
        make.left.equalTo(self.xxpk_codeTextField.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.right.equalTo(self.xxpk_mobileTextField);
    }];
    
    // 提高按钮水平的 content hugging 优先级，让它不会拉伸填充剩余空间
    [self.xxpk_codeButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    // 密码
    self.xxpk_passwordTextField = [XXGUIDriver xxpk_textFieldOfPassword:YES];
    [self.view addSubview:self.xxpk_passwordTextField];
    [self.xxpk_passwordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_codeTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    UIButton *nweRightButton = self.xxpk_passwordTextField.rightView.subviews.firstObject;
    [nweRightButton addTarget:self action:@selector(__xxpk_textFieldButtonClickHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    // 找回
    UIButton *xxpk_forgetButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_forgetKey];
    [xxpk_forgetButton addTarget:self action:@selector(xxpk_forgetButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_forgetButton];
    [xxpk_forgetButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_passwordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
}
- (void)__xxpk_textFieldButtonClickHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.xxpk_passwordTextField.secureTextEntry = !self.xxpk_passwordTextField.isSecureTextEntry;
}

- (void)xxpk_forgetButtonAction:(id)sender {
    if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
        return;
    }
    if (self.xxpk_codeTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_code_verified completion:nil];
        return;
    }
    if (self.xxpk_passwordTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_boxkey_verified completion:nil];
        return;
    }
    NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
    NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_forgetButtonDidClickWithMobile:code:dialCode:newKey:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_forgetButtonDidClickWithMobile:xxpk_mobile code:self.xxpk_codeTextField.text dialCode:xxpk_dialCode newKey:self.xxpk_passwordTextField.text completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
            [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_forgetKey_sus];
            if (object) {
                [self.xxpk_forgetDelegate xxpk_foregetFinishWithName:object xxpk_forgetPassword:self.xxpk_passwordTextField.text];
                [self xxpk_backButtonAction:nil];
            }
        }];
    }
}

- (void)dealloc {
    [self.xxpk_codeButton xxpk_stopCountdown];
}
@end
