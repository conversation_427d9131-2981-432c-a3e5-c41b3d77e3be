//
//  XXGMobileViewController.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/10.
//

#import "XXGMobileViewController.h"
#import "XXGSendCodeButton.h"
#import "XXGContentTextViewController.h"
#import "XXGToast.h"
#import "XXGProtocolLabel.h"
#import "XXGMobileTextField.h"
#import "NSString+XXGString.h"

@interface XXGMobileViewController ()

@property (nonatomic, strong) XXGMobileTextField *xxpk_mobileTextField;
@property (nonatomic, strong) UITextField *xxpk_codeTextField;
@property (nonatomic, strong) XXGSendCodeButton *xxpk_codeButton;
@property (nonatomic,strong) XXGProtocolLabel *xxpk_protoclLabel;

@end

@implementation XXGMobileViewController

- (XXGSendCodeButton *)xxpk_codeButton {
    if (!_xxpk_codeButton) {
        _xxpk_codeButton = [[XXGSendCodeButton alloc] init];
    }
    return _xxpk_codeButton;
}

- (XXGProtocolLabel *)xxpk_protoclLabel {
    if (!_xxpk_protoclLabel) {
        _xxpk_protoclLabel = [XXGProtocolLabel xxpk_protocolLabel:NO];
    }
    return _xxpk_protoclLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // xx手机号
    self.xxpk_mobileTextField = [[XXGMobileTextField alloc] initWithController:self];
    [self.view addSubview:self.xxpk_mobileTextField];
    [self.xxpk_mobileTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float45);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    // 验证码xx
    self.xxpk_codeTextField = [XXGUIDriver xxpk_textFieldOfVerificationCode];
    [self.view addSubview:self.xxpk_codeTextField];
    [self.xxpk_codeTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_mobileTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    spaRows(self);
    self.xxpk_codeButton.xxpk_sendCodeAction = ^{
        selectJob(self);
        NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
        NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
        if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
            [self.xxpk_codeButton xxpk_stopCountdown];
            [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
            return;
        }
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_sendCodeButtonDidClickWithType:xxpk_moblil:dialCode:completion:)]) {
            [XXGLoadingView showLoadingOnWindow];
            [self.xxpk_delegate xxpk_sendCodeButtonDidClickWithType:XXGUIDriver.xxpk_data_ui.xxpk_code_comein xxpk_moblil:xxpk_mobile dialCode:xxpk_dialCode completion:^(id object) {
                [XXGLoadingView hideLoadingFromWindow];
                if ([object boolValue]) {
                    [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_sendedVerificationCode];
                }else {
                    [self.xxpk_codeButton xxpk_stopCountdown];
                }
            }];
        }
    };
    [self.view addSubview:self.xxpk_codeButton];
    [self.xxpk_codeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.xxpk_codeTextField);
        make.height.equalTo(self.xxpk_codeTextField);
        make.left.equalTo(self.xxpk_codeTextField.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.right.equalTo(self.xxpk_mobileTextField);
    }];
    
    // 提高按钮水平的 content hugging 优先级，让它不会拉伸填充剩余空间
    [self.xxpk_codeButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    // 登录/注册按钮
    UIButton *xxpk_registerButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_comein_mobileAndReg];
    [xxpk_registerButton addTarget:self action:@selector(xxpk_registerButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_registerButton];
    [xxpk_registerButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_codeTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    [self.view addSubview:self.xxpk_protoclLabel];
    [self.xxpk_protoclLabel setXxpk_handleProtocolTap:^{
        selectJob(self);
        [self xxpk_userProtocolButtonAction];
    }];
    [self.xxpk_protoclLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float18);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float20);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float20);
    }];
}

- (void)xxpk_registerButtonAction:(UIButton *)sender {
    if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
        return;
    }
    if (self.xxpk_codeTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_code_verified completion:nil];
        return;
    }
    NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
    NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_mobileComeinButtonDidClickWithMobile:code:dialCode:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_mobileComeinButtonDidClickWithMobile:xxpk_mobile code:self.xxpk_codeTextField.text dialCode:xxpk_dialCode completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}

/// xx用户协议
- (void)xxpk_userProtocolButtonAction {
    XXGContentTextViewController *agreement_vc = [XXGContentTextViewController new];
    agreement_vc.xxpk_object = @(YES);
    agreement_vc.xxpk_delegate = self.xxpk_delegate;
    [agreement_vc setXxpk_completion:^(BOOL result) {
        self.xxpk_protoclLabel.xxpk_isChecked = result;
    }];
    [self.navigationController pushViewController:agreement_vc animated:NO];
}

- (void)dealloc {
    [self.xxpk_codeButton xxpk_stopCountdown];
}
@end
