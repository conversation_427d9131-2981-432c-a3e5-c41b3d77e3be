//
//  XXGNetwork.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/24.
//

#import "XXGNetwork.h"
#import "XXGNetworkCore.h"
#import "NSData+SunHope.h"
#import "XXGPlayKitConfig.h"
#import "XXGStartBody.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "ZBObjectiveCBeaver.h"

#define spaRows(obj) __weak typeof(obj) weak##obj = obj;
#define selectJob(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGNetwork ()
@property (nonatomic, assign) NSUInteger xxpk_retryCount; // 记录重试次数
@end

@implementation XXGNetwork

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.xxpk_retryCount = 6;
    }
    return self;
}

+ (instancetype)xxpk_defaultNetwork {
    id instance = [[super alloc] init];
    return instance;
}

- (NSMutableDictionary *)xxpk_networkParams:(NSDictionary *)params {
    NSMutableDictionary *xxpk_networkParams = [params mutableCopy];
    xxpk_networkParams[yearsExpand.xxpk_timestamp] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    XXGBoxContent *model = [XXGBoxManager xxpk_comeinedBox];
    if (model) {
        xxpk_networkParams[yearsExpand.xxpk_box] = @{
            yearsExpand.xxpk_token:model.xxpk_boxToken?:@"",
            yearsExpand.xxpk_id:model.xxpk_boxId?:@""
        };
    }
    return xxpk_networkParams;
}

- (NSMutableURLRequest *)xxpk_networkRequest:(NSString *)url paramsData:(NSData *)paramsData {
    // 处理Data
    NSData *data = [paramsData enDataZip];
    // 签名
    NSString *xsign = [data xxpk_sign:XXGPlayKitConfig.shared.xxpk_security];
    // 拼接url
    NSString *urlString = [url stringByAppendingString:[NSString stringWithFormat:yearsExpand.xxpk_sign, xsign]];
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlString]];
    
    // 设置头部参数
    [request addValue:yearsExpand.xxpk_gzip forHTTPHeaderField:yearsExpand.xxpk_content_encoding];
    [request addValue:yearsExpand.xxpk_application_json forHTTPHeaderField:yearsExpand.xxpk_content_type];
    [request setHTTPMethod:yearsExpand.xxpk_http_method];
    
    // body
    [request setHTTPBody:data];
    
    return request;
}

- (void)xxpk_sendRequest:(NSString *)url
                  params:(NSDictionary *)params
                 success:(void(^)(NSDictionary *responseObject))success
                 failure:(void(^)(NSError *error))failure {
    
    NSMutableDictionary *networkParams = [self xxpk_networkParams:params?:@{}];
    _xxpk_url = url;
    
    LeastRequest(url, networkParams);
    
    NSError *error = nil;
    NSData *paramsData = [NSJSONSerialization dataWithJSONObject:networkParams?:@{} options:(NSJSONWritingPrettyPrinted) error:&error];
    if (error) {
        if (failure) {
            failure(error);
        }
    }
    NSMutableURLRequest *request = [self xxpk_networkRequest:url paramsData:paramsData];
    [[XXGNetworkCore shared] xxpk_sendBaseRequest:request process:^NSData * _Nullable(NSData * _Nullable rawData) {
        return [rawData unDataZip];;
    } success:^(NSDictionary * _Nonnull responseObject) {
        
        BasalResponse(url, responseObject);
        
        [self xxpk_sendRequestSuccessWithUrl:url responseObject:responseObject params:params success:success failure:failure];
        
    } failure:^(NSError * _Nonnull error) {
        
        IconTargetSexWelshStepchild(url, error);
        
        if (failure) {
            failure(error);
        }
    } retryCount:self.xxpk_retryCount];
}

- (void)xxpk_sendRequestSuccessWithUrl:(NSString *)url
                        responseObject:(NSDictionary *)responseObject
                                params:(NSDictionary *)params
                               success:(void(^)(NSDictionary *responseObject))success
                               failure:(void(^)(NSError *error))failure {
    
    NSString *status = responseObject[yearsExpand.xxpk_status];
    
    if ([status isEqualToString:yearsExpand.xxpk_redirect]) {
        [self xxpk_sendRequest:responseObject[yearsExpand.xxpk_url] params:params success:success failure:failure];
    }
    
    if ([status isEqualToString:yearsExpand.xxpk_error]) {
        if (failure) {
            failure([NSError errorWithDomain:yearsExpand.xxpk_network
                                        code:yearsExpand.xxpk_net_code_error
                                    userInfo:@{NSLocalizedDescriptionKey : responseObject[yearsExpand.xxpk_errmsg]}]);
        }
    }
    
    if ([status isEqualToString:yearsExpand.xxpk_ok]) {
        if (success) {
            success(responseObject);
            if ([responseObject[yearsExpand.xxpk_tip] length] > 0) {
                [XXGAlertView xxpk_showAlertWithTitle:maleKilometer.xxpk_tips message:responseObject[yearsExpand.xxpk_tip] completion:nil];
            }
        }
    }
    
    if ([status isEqualToString:yearsExpand.xxpk_recomein]) {
        [self xxpk_recomeinWithUrl:url params:params success:success failure:failure];
    }
}

- (void)xxpk_recomeinWithUrl:(NSString *)url
                      params:(NSDictionary *)params
                     success:(void(^)(NSDictionary *responseObject))success
                     failure:(void(^)(NSError *error))failure {}

- (void)dealloc {
    NSLog(@"%s - %@", __func__, _xxpk_url);
}
@end
