//
//  XXGBaseURL.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/24.
//

#import "XXGBaseURL.h"
#import "XXGPlayKitConfig.h"

@implementation XXGBaseURL

- (instancetype)init
{
    self = [super init];
    if (self) {
        
// 海外地址
        self.xxpk_baseUrls = @[yearsExpand.xxpk_base_os_url];

    }
    return self;
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (NSString *)xxpk_currentBaseUrl {
    return [NSString stringWithFormat:yearsExpand.xxpk_base_fix, self.xxpk_baseUrls[self.xxpk_baseUrlIdx]];
}

- (void)xxpk_next {
    self.xxpk_baseUrlIdx++;
    if (self.xxpk_baseUrlIdx > self.xxpk_baseUrls.count-1) {
        self.xxpk_baseUrlIdx = 0;
    }
}

- (NSInteger)xxpk_currentBaseUrlIdx {
    NSUserDefaults * userDefault = [NSUserDefaults standardUserDefaults];
    return ![userDefault objectForKey:yearsExpand.xxpk_base_url_k] ? 0 : [[userDefault objectForKey:yearsExpand.xxpk_base_url_k] integerValue];
}

- (void)xxpk_setCurrentBaseUrlIdx {
    NSUserDefaults * userDefault = [NSUserDefaults standardUserDefaults];
    [userDefault setObject:@(self.xxpk_baseUrlIdx) forKey:yearsExpand.xxpk_base_url_k];
    [userDefault synchronize];
}
@end
