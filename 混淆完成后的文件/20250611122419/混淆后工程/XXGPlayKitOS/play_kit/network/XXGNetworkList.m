//
//  XXGNetworkList.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/3.
//

#import "XXGNetworkList.h"
#import "XXGNetwork.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"
#import "XXGNetworkMonitor.h"
#import "XXGBaseURL.h"
#import "NSData+SunHope.h"
#import "NSString+XXGString.h"
#import "XXGBoxManager.h"
#import "XXGSkinModel.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"

@implementation XXGNetworkList

// MARK: - 初始化
- (void)xxpk_networkStart:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure  {
    
    NSDictionary *params = [[XXGPlayKitConfig shared].xxpk_body xxpk_modelToDict];
    params[yearsExpand.xxpk_device][yearsExpand.xxpk_extend] = [[XXGPlayKitConfig shared].xxpk_deviceInfo xxpk_modelToDict];
    
    [self xxpk_sendRequest:XXGBaseURL.shared.xxpk_currentBaseUrl params:params success:^(NSDictionary * _Nonnull responseObject) {
        
        [XXGPlayKitConfig shared].xxpk_netList = [XXGNetListModel xxpk_modelWithDict:responseObject[yearsExpand.xxpk_api_list]];
        
        [XXGPlayKitConfig shared].xxpk_security = responseObject[yearsExpand.xxpk_app][yearsExpand.xxpk_secret];
        
        [XXGPlayKitConfig shared].xxpk_body.xxpk_deviceId = responseObject[yearsExpand.xxpk_device][yearsExpand.xxpk_id];
        
        [XXGPlayKitConfig shared].xxpk_adaptionCof = [XXGAdaptionCof xxpk_modelWithDict:responseObject[yearsExpand.xxpk_config]];
        
        [XXGPlayKitConfig shared].xxpk_serverInfo = [XXGServerInfo xxpk_modelWithDict:responseObject[yearsExpand.xxpk_server]];

[XXGPlayKitConfig shared].xxpk_extraParams = [XXGExtraParams xxpk_modelWithDict:responseObject[yearsExpand.xxpk_extra_params_str]];
        
        if (success) {
            success(responseObject);
        }
        [[XXGBaseURL shared] xxpk_setCurrentBaseUrlIdx];
        
    } failure:^(NSError * _Nonnull error) {
        if (!XXGNetworkMonitor.xxpk_isConnected || error.code == yearsExpand.xxpk_net_code_error) {
            if (failure) {
                failure(error);
            }
        }else {
            [[XXGBaseURL shared] xxpk_next];
            [self xxpk_networkStart:success failure:failure];
        }
    }];
}

- (void)xxpk_handleNetworkComeinSuccess:(XXGBoxContent *)box {
    
    box.xxpk_lastComeinTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    
    // 检测本地是否有相同行号存在
    XXGBoxContent *xxpk_comeined_box =[XXGBoxManager xxpk_localBoxContentWithBoxName:box.xxpk_boxName];
    if (xxpk_comeined_box) {
        box.xxpk_boxType = xxpk_comeined_box.xxpk_boxType;
    }
    
    // 当前用户
    [XXGBoxManager xxpk_setComeinedBox:box];
    
    // 存储列表
    [XXGBoxManager xxpk_saveBoxContentToLocal:box];
}

- (NSString *)xxpk_handleNetworkComeinUrl:(XXGComeinType)type {
    // 使用静态字典保证线程安全且只初始化一次
    static NSDictionary<NSNumber *, NSString *> *map;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        map = @{
            // 基础动作类型
            @(XXGComeinTypeGuest)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_guest?:@"",
            @(XXGComeinTypeRegister)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
            @(XXGComeinTypeAccount)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein?:@"",
            @(XXGComeinTypeMobile)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_mobile?:@"",
            @(XXGComeinTypeToken)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_comein_token?:@"",

@(XXGComeinTypeFacebook)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
            @(XXGComeinTypeVK)  : XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_register?:@"",
        };
    });
    
    // 安全访问并转换类型
    return map[@(type)];
}

// MARK: - 重新登录
- (void)xxpk_recomeinWithUrl:(NSString *)url
                      params:(NSDictionary *)params
                     success:(void(^)(NSDictionary *responseObject))success
                     failure:(void(^)(NSError *error))failure {
    if ([self.xxpk_url isEqual:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeToken]]) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        [self xxpk_networkAccountWithBoxName:xxpk_box.xxpk_boxName boxKey:xxpk_box.xxpk_boxKey success:success failure:failure];
    }else {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        [self xxpk_networkAccountWithBoxName:xxpk_box.xxpk_boxName boxKey:xxpk_box.xxpk_boxKey success:^(NSDictionary * _Nonnull responseObject) {
            [self xxpk_sendRequest:url params:params success:success failure:failure];
        } failure:^(NSError * _Nonnull error) {
            if (error.code == yearsExpand.xxpk_net_code_error) {
                [XXGPlayKitCore.shared xxpk_logout];
                [XXGAlertView xxpk_showAlertWithTitle:maleKilometer.xxpk_comeinError message:error.localizedDescription completion:nil];
            }else {
                failure(error);
            }
        }];
    }
}

// MARK: - 注册
- (void)xxpk_networkRegisterWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_boxName] = boxName;
    _params[yearsExpand.xxpk_boxKey] = boxKey;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeRegister] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeRegister;
        xxpk_box.xxpk_boxName = boxName;
        xxpk_box.xxpk_boxKey = boxKey;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 账号登录
/*
 boxkey md5值
 */
- (void)xxpk_networkAccountWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_boxName] = boxName;
    _params[yearsExpand.xxpk_boxKey] = boxKey;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeAccount] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeAccount;
        xxpk_box.xxpk_boxKey = boxKey;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 游客登录
- (void)xxpk_networkGuest:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_localBoxContentWithBoxType:(XXGComeinTypeGuest)];
    if (xxpk_box) {
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        [self xxpk_networkToken:success failure:failure];
        return;
    }
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeGuest;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
        
        [[XXGPlayKitCore shared] xxpk_showUIofSavePS:@{
            yearsExpand.xxpk_boxName:xxpk_box.xxpk_boxName,
            yearsExpand.xxpk_boxKey:responseObject[yearsExpand.xxpk_box][yearsExpand.xxpk_boxKey],
        }];
    } failure:failure];
}

// MARK: - Facebook登录
- (void)xxpk_networkFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_oauth] = @{
        yearsExpand.xxpk_net_src:yearsExpand.xxpk_net_src_facebook,
        yearsExpand.xxpk_box:@{
            yearsExpand.xxpk_id:uid?:@"",
            yearsExpand.xxpk_token:uToken?:@"",
            yearsExpand.xxpk_net_auth_token:authToken?:@"",
            yearsExpand.xxpk_net_nonce:nonce?:@""
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_fbBind = YES;
        xxpk_box.xxpk_facebookUid = uid;
        xxpk_box.xxpk_facebookToken = uToken;
        xxpk_box.xxpk_facebookAuthToken = authToken;
        xxpk_box.xxpk_facebookNonce = nonce;
        xxpk_box.xxpk_boxType = XXGComeinTypeFacebook;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 绑定Facebook
- (void)xxpk_networkBindFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_oauth] = @{
        yearsExpand.xxpk_net_src:yearsExpand.xxpk_net_src_facebook,
        yearsExpand.xxpk_box:@{
            yearsExpand.xxpk_id:uid?:@"",
            yearsExpand.xxpk_token:uToken?:@"",
            yearsExpand.xxpk_net_auth_token:authToken?:@"",
            yearsExpand.xxpk_net_nonce:nonce?:@""
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_facebook_auth params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_fbBind = YES;
        xxpk_box.xxpk_facebookUid = uid;
        xxpk_box.xxpk_facebookToken = uToken;
        xxpk_box.xxpk_facebookAuthToken = authToken;
        xxpk_box.xxpk_facebookNonce = nonce;
        // 当前用户
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        // 存储列表
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - VK登录
- (void)xxpk_networkVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_oauth] = @{
        yearsExpand.xxpk_net_src:yearsExpand.xxpk_net_src_vk,
        yearsExpand.xxpk_box:@{
            yearsExpand.xxpk_id:uid?:@"",
            yearsExpand.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_vkBind = YES;
        xxpk_box.xxpk_vkUid = uid;
        xxpk_box.xxpk_vkToken = uToken;
        xxpk_box.xxpk_boxType = XXGComeinTypeVK;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 绑定VK
- (void)xxpk_networkBindVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_oauth] = @{
        yearsExpand.xxpk_net_src:yearsExpand.xxpk_net_src_vk,
        yearsExpand.xxpk_box:@{
            yearsExpand.xxpk_id:uid?:@"",
            yearsExpand.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_vk_auth params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_vkBind = YES;
        xxpk_box.xxpk_vkUid = uid;
        xxpk_box.xxpk_vkToken = uToken;
        // 当前用户
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        // 存储列表
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 渠道Poopo登录
- (void)xxpk_networkPoopoWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_oauth] = @{
        yearsExpand.xxpk_net_src:yearsExpand.xxpk_net_src_poopo,
        yearsExpand.xxpk_box:@{
            yearsExpand.xxpk_id:uid?:@"",
            yearsExpand.xxpk_token:uToken?:@"",
        }
    };
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeGuest] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypePoopo;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 上报AdjustId
- (void)xxpk_networkReportAdjustId:(NSString *)arg {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_net_real_adjid] = arg;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_adjustid_report params:_params success:nil failure:nil];
}

// MARK: - 上报日志给cp调用
- (void)xxpk_networkReportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_data] = @{
        yearsExpand.xxpk_type:xxpk_type?:@"",
        yearsExpand.xxpk_content:xxpk_content?:@""
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_test_report params:_params success:nil failure:nil];
}

// MARK: - Token
- (void)xxpk_networkToken:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeToken] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        NSString *xxpk_box_token = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]].xxpk_boxToken;
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_boxToken = xxpk_box_token;
        [self xxpk_handleNetworkComeinSuccess:xxpk_box];
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 获取验证码
- (void)xxpk_networkVerifyCodeType:(NSString *)type mobile:(NSString *)xxpk_mobile dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    // 请求参数
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_mobile] = xxpk_mobile;
    _params[yearsExpand.xxpk_purpose] = type;
    _params[yearsExpand.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_sms_code params:_params success:success failure:failure];
}

// MARK: - 手机登录
- (void)xxpk_networkMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
   NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_mobile] = xxpk_mobile;
    _params[yearsExpand.xxpk_sms_code] = code;
    _params[yearsExpand.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:[self xxpk_handleNetworkComeinUrl:XXGComeinTypeMobile] params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:responseObject[yearsExpand.xxpk_box]];
        xxpk_box.xxpk_boxType = XXGComeinTypeMobile;
        xxpk_box.xxpk_boxMobile = xxpk_mobile;
        xxpk_box.xxpk_boxKey = xxpk_box.xxpk_boxKey.md5.lowercaseString;
       [self xxpk_handleNetworkComeinSuccess:xxpk_box];
       if (success) {
           success(responseObject);
       }
   } failure:failure];
}

// MARK: - 找回密码
- (void)xxpk_networkForgetKeyWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode newKey:(NSString *)newKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure  {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_mobile] = xxpk_mobile;
    _params[yearsExpand.xxpk_sms_code] = code;
    _params[yearsExpand.xxpk_new_key] = newKey;
    _params[yearsExpand.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_password_reset params:_params success:^(NSDictionary * _Nonnull responseObject) {
        
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_localBoxContentWithBoxName:responseObject[yearsExpand.xxpk_box][yearsExpand.xxpk_name]];
        xxpk_box.xxpk_boxKey = newKey;
        // 存储列表
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        
        if (success) {
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 修改密码
- (void)xxpk_networkChangeBoxKeyWithOldKBoxKey:(NSString *)oldBoxKey newBoxKey:(NSString *)newBoxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_old_key] = oldBoxKey;
    _params[yearsExpand.xxpk_new_key] = newBoxKey;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_password_change params:_params success:^(NSDictionary * _Nonnull responseObject) {
        XXGBoxContent *xxpk_box = [XXGBoxManager xxpk_comeinedBox];
        xxpk_box.xxpk_boxKey = newBoxKey;
        [XXGBoxManager xxpk_setComeinedBox:xxpk_box];
        [XXGBoxManager xxpk_saveBoxContentToLocal:xxpk_box];
        if (success) {
            [self xxpk_networkToken:nil failure:nil];
            success(responseObject);
        }
    } failure:failure];
}

// MARK: - 绑定手机
- (void)xxpk_networkBindMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    _params[yearsExpand.xxpk_mobile] = xxpk_mobile;
    _params[yearsExpand.xxpk_sms_code] = code;
    _params[yearsExpand.xxpk_dial_code] = dialCode;
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_bind_mobile params:_params success:success failure:failure];
}

// MARK: - 下单
- (void)xxpk_networkCreateOrder:(BOOL)isCoin params:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSString *url = isCoin ?XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_coin_booking:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking;
    [self xxpk_sendRequest:url params:params success:success failure:failure];
}

// MARK: - iap验单
- (void)xxpk_networkValidateReceipt:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_receipt params:params success:success failure:failure];
}

// MARK: - 获取优惠券
- (void)xxpk_networkOrderExtra:(NSString *)xxpk_orderId pmethod:(NSString *)pmethod success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSDictionary *_params = @{
        yearsExpand.xxpk_order:@{
            yearsExpand.xxpk_id:xxpk_orderId,
            yearsExpand.xxpk_pm:pmethod
        }
    };
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_extra params:_params success:success failure:failure];
}

// MARK: - H5验单
- (void)xxpk_networkCheckOrderWithIsCoin:(BOOL)isCoin
                            xxpk_orderId:(NSString *)xxpk_orderId
                                 success:(void(^)(NSDictionary *responseObject))success
                                 failure:(void(^)(NSError *error))failure
                              retryCount:(NSInteger)retryCount
                          currentAttempt:(NSInteger)currentAttempt {
    NSString *url = isCoin ?XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_coin_booking_check:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_booking_check;
    NSMutableDictionary *params = [NSMutableDictionary new];
    params[yearsExpand.xxpk_order] = @{yearsExpand.xxpk_id:xxpk_orderId};
    [self xxpk_sendRequest:url params:params success:^(NSDictionary * _Nonnull responseObject) {
        NSInteger status = [responseObject[yearsExpand.xxpk_order][yearsExpand.xxpk_p_status] integerValue];
        if ((status == 0) && (currentAttempt < retryCount)) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self xxpk_networkCheckOrderWithIsCoin:isCoin xxpk_orderId:xxpk_orderId success:success failure:failure retryCount:retryCount currentAttempt:currentAttempt+1];
            });
        }else {
            if (success) success(responseObject);
        }
    } failure:failure];
}

// MARK: - 上报角色
- (void)xxpk_networkUploadRoleInfo:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_role params:params success:success failure:failure];
}

// MARK: - 请求mqtt参数
- (void)xxpk_networkMqtt:(void(^)(NSDictionary *responseObject))success {
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_subscribe params:nil success:success failure:^(NSError * _Nonnull error) {
        if (error.code != yearsExpand.xxpk_net_code_error) {
            [self xxpk_networkMqtt:success];
        }
    }];
}

// MARK: - 注销
- (void)xxpk_networkRemoveAccount:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure {
    NSMutableDictionary *_params = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    [self xxpk_sendRequest:XXGPlayKitConfig.shared.xxpk_netList.xxpk_list_account_remove params:_params success:success failure:failure];
}
@end
