//
//  XXGPlayKitConfig.h
//  XXGPlayKit
//
//  Created by apple on 2025/2/26.
//

#import <Foundation/Foundation.h>
#import "XXGSetting.h"
#import "XXGNetListModel.h"
#import "XXGStartBody.h"
#import "XXGDeviceInfo.h"
#import "XXGAdaptionCof.h"
#import "XXGLocalizedCore.h"
#import "XXGDatasCore.h"
#import "XXGExtraParams.h"
#import "XXGServerInfo.h"

NS_ASSUME_NONNULL_BEGIN

#define yearsExpand XXGPlayKitConfig.shared.xxpk_data_core

#define maleKilometer XXGPlayKitConfig.shared.xxpk_string_core

@interface XXGPlayKitConfig : NSObject

+ (instancetype)shared;

// MARK: - 初始化返回数据
@property (nonatomic, strong) XXGNetListModel *xxpk_netList;

@property (nonatomic, strong) XXGStartBody *xxpk_body;

@property (nonatomic, strong) XXGDeviceInfo *xxpk_deviceInfo;

@property (nonatomic, strong) XXGAdaptionCof *xxpk_adaptionCof;

@property (nonatomic, strong) XXGExtraParams *xxpk_extraParams;

@property (nonatomic, strong) XXGServerInfo *xxpk_serverInfo;

// MARK: - 其他本地变量
@property (nonatomic, strong) XXGLocalizedCore *xxpk_string_core;

@property (nonatomic, strong) XXGDatasCore *xxpk_data_core;

@property (nonatomic, copy) NSString *xxpk_startid;

@property (nonatomic, copy) NSString *xxpk_security;

@property (nonatomic, assign) BOOL xxpk_trampoline;

@property (nonatomic, copy) NSString *xxpk_deeplink;

@property (nonatomic, copy) NSString *xxpk_current_sdkname;

@property (nonatomic, assign) XXGPlayKitStartStatus xxpk_startStatus;

@property (nonatomic, assign) XXGPlayKitComeInStatus xxpk_comeinStatus;

/// 是否是渠道包
@property (nonatomic, assign) BOOL xxpk_isCanal;

// 渠道列表
@property (nonatomic, assign) BOOL xxpk_isPoopo;

// MARK: - 设置变量
@property (nonatomic, assign) BOOL xxpk_closeButtonHidden;

@property (nonatomic, copy) NSString *xxpk_testAppId;

@property (nonatomic, copy) NSString *xxpk_testBundleId;

@property (nonatomic, copy) NSString *xxpk_testAppVersion;

@end

NS_ASSUME_NONNULL_END
