//
//  XXGWKMethodAction.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/24.
//

#import "XXGWKMethodAction.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"
#import "XXGUIKit.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "NSString+URLEncoding.h"
#import "XXGIAPManager.h"
#import "NSObject+XXGModel.h"
#import "XXGNetworkList.h"
#import "XXGToast.h"
#import "NSURL+XXGAnalyse.h"
#import "XXGPlayKitCore+Others.h"
#import "XXGProductBody.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGWKMethodAction

+ (void)xxpk_wkView:(WKWebView *)wkView makeMethodAction:(NSString *)method arg:(id)arg {
    EightInfo(@"WebView事件-%@",method);
    if (method.xxpk_isEmpty) {
        return;
    }
    if ([method isEqualToString:yearsExpand.xxpk_core_mt_changep]) { // changePassword
        [XXGPlayKitCore.shared xxpk_showUIofChangeBoxKey:wkView];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_bindm]) {// bindMobile
        [XXGPlayKitCore.shared xxpk_showUIofbindMobile:@(NO) hasWkView:wkView];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_switcha]) {// switchAccount
        [self __xxpk_wk_switchAccount];
    }else if ([method isEqualToString:yearsExpand.xxpk_recomein]) {// relogin
        [XXGPlayKitCore.shared xxpk_logout];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_openURL]) {// openURL
        [self __xxpk_openUrl:arg];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_ucenter]) {// ucenter
        [self __xxpk_wk_ucenter:arg];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_iapRepair]) {// iapRepair
        [self __xxpk_wk_iapRepair:arg];
    }else if ([method isEqualToString:yearsExpand.xxpk_core_mt_getInfomation]) {// getInfomation
        [self __xxpk_wk_getInfomation:wkView];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_accountRemove]) {// accountRemove
        [self __xxpk_removeAccount];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_getApiUrl]) {// getApiUrl
        [self __xxpk_getApiUrl:wkView];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_getToken]) {// gettoken
        [self __xxpk_getToken:wkView];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_popup]) {// popup
        [self __xxpk_popup:arg];
    }
    
    else if ([method isEqualToString:yearsExpand.xxpk_core_mt_userInfoSub]||
              [method isEqualToString:yearsExpand.xxpk_core_mt_closeSplash]) { //userInfoSub & closeSplash
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
    
    else if([method isEqualToString:yearsExpand.xxpk_core_mt_openUserCenterSidebar]) {//openUserCenterSidebar
        [XXGPlayKitCore.shared xxpk_openUserCenterSidebar:arg];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_coin_p]) {//coinP
        [self __xxpk_coinP:arg];
    }
    
else if([method isEqualToString:yearsExpand.xxpk_core_mt_facebookShare]) {
        [self __xxpk_facebookShare:arg];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_facebookSub]) {
        [self __xxpk_facebookSub];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_facebookBind]) {
        [self __xxpk_facebookBind];
    }else if([method isEqualToString:yearsExpand.xxpk_core_mt_facebookInvite]) {
        [self __xxpk_facebookInvite];
    }
}

// WKScriptMessageHandler
+ (void)__xxpk_coinP:(NSString *)json {
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    if (!dic) {
        return;
    }
    XXGProductBody *body = [XXGProductBody xxpk_modelWithDict:dic];
    [XXGPlayKitCore.shared xxpk_creatOrder:body xxpk_isCoinOrder:YES];
}

+ (void)__xxpk_popup:(NSURL *)url {
    NSDictionary *ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    if ([ext[yearsExpand.xxpk_action] isEqualToString:yearsExpand.xxpk_open]) {
        [[XXGPlayKitCore shared] xxpk_showUIofPopup:ext];
    }else {
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
}

+ (void)__xxpk_getToken:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:yearsExpand.xxpk_core_mt_func_getToken,[XXGBoxManager xxpk_comeinedBox].xxpk_boxToken].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        NSLog(@"%@----%@",resultValue, error);
    }];
}

+ (void)__xxpk_getApiUrl:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:yearsExpand.xxpk_core_mt_func_getApiUrl,XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        NSLog(@"%@----%@",resultValue, error);
    }];
}

+ (void)__xxpk_removeAccount {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkRemoveAccount:^(NSDictionary * _Nonnull responseObject) {
        [XXGPlayKitCore.shared xxpk_logout];
        [XXGToast showBottom:maleKilometer.xxpk_account_removed];
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:yearsExpand.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:maleKilometer.xxpk_tips message:errorDescrip completion:nil];
    }];
}

+ (void)__xxpk_openUrl:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    [XXGPlayKitCore.shared xxpk_coreHandleOpenUrl:ext[yearsExpand.xxpk_url]];
}

// 打开用户中心
+ (void)__xxpk_wk_ucenter:(NSURL *)url {
    
    NSString *query = url.query;
    
    if (query.xxpk_isNotEmpty && query.length > 4) {
        query = [query substringFromIndex:4]; // 取url= 后面字符
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:query.xxpk_urlDecodedString];
    }else {
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url];
    }
}

/// 内购修复
+ (void)__xxpk_wk_iapRepair:(NSURL *)url {
    [XXGPlayKitCore.shared xxpk_iapRepair];
}

+ (void)__xxpk_wk_getInfomation:(WKWebView *)vkview {
    NSMutableDictionary *dic = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    XXGBoxContent *box = [XXGBoxManager xxpk_comeinedBox];
    NSMutableDictionary *udic = [NSMutableDictionary new];
    udic[yearsExpand.xxpk_core_mt_getInfomation_uid] = box.xxpk_boxId;
    udic[yearsExpand.xxpk_core_mt_getInfomation_name] = box.xxpk_boxName;
    udic[yearsExpand.xxpk_core_mt_getInfomation_token] = box.xxpk_boxToken;
udic[yearsExpand.xxpk_core_mt_getInfomation_fbuid] = box.xxpk_facebookUid;
    udic[yearsExpand.xxpk_core_mt_getInfomation_fbtoken] = box.xxpk_facebookToken;
    udic[yearsExpand.xxpk_core_mt_getInfomation_fbauthtoken] = box.xxpk_facebookAuthToken;
    udic[yearsExpand.xxpk_core_mt_getInfomation_fbnonce] = box.xxpk_facebookNonce;
    dic[yearsExpand.xxpk_core_mt_getInfomation_user] = udic;
    NSData *data = [NSJSONSerialization dataWithJSONObject:dic options:kNilOptions error:nil];
    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString * jsFunc = [NSString stringWithFormat:yearsExpand.xxpk_core_mt_func_getInfomation,string].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"returnInfomation %@----%@",result, error);
    }];
}

+ (void)__xxpk_wk_switchAccount {
    [XXGAlertView xxpk_showAlertWithTitle:maleKilometer.xxpk_tips message:maleKilometer.xxpk_switchBox buttonTitles:@[maleKilometer.xxpk_ok,maleKilometer.xxpk_cancel] completion:^(NSInteger buttonIndex) {
        if (buttonIndex == 0) {
            [XXGPlayKitCore.shared xxpk_logout];
        }
    }];
}

+ (void)__xxpk_facebookShare:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    NSString *linkUrl = ext[yearsExpand.xxpk_url];
    NSString *imgUrl = ext[yearsExpand.xxpk_imgUrl];
    if (linkUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithUrl:linkUrl];
        return;
    }
    if (imgUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithImgUrl:imgUrl];
        return;
    }
}

+ (void)__xxpk_facebookSub {
    [XXGPlayKitCore xxpk_facebookSub];
}

+ (void)__xxpk_facebookBind {
    [XXGPlayKitCore xxpk_facebookBind:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
        if (errorMsg) {
            [XXGAlertView xxpk_showAlertWithTitle:maleKilometer.xxpk_tips message:errorMsg completion:nil];
        }else {
            [XXGToast showBottom:maleKilometer.xxpk_bind_sus];
        }
    }];
}

+ (void)__xxpk_facebookInvite {
    [XXGPlayKitCore xxpk_facebookInvite];
}

@end
