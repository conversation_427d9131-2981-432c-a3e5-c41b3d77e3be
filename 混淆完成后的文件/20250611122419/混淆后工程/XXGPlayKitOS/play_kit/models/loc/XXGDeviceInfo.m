//
//  XXGDeviceInfo.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/28.
//

#import "XXGDeviceInfo.h"
#import "XXGAppInfo.h"
#import "XXGSecurityCheckTool.h"
#import "XXGNetworkMonitor.h"
#import "XXGPlayKitConfig.h"
@import UIKit;

#import "XXGAppsFlyerManager.h"
#import "XXGFirebaseManager.h"

@implementation XXGDeviceInfo

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return yearsExpand.xxpk_device_info;
}

- (NSString *)xxpk_name {
    return XXGAppInfo.xxpk_deviceName;
}

- (NSString *)xxpk_idfa {
    return XXGAppInfo.xxpk_deviceIdfa;
}

- (NSString *)xxpk_idfv {
    return XXGAppInfo.xxpk_deviceIdfv;
}

- (NSString *)xxpk_model {
    return XXGAppInfo.xxpk_deviceModel;
}

- (NSString *)xxpk_os {
    return yearsExpand.xxpk_ios;
}

- (NSString *)xxpk_osVersion {
    return XXGAppInfo.xxpk_systemVersion;
}

- (NSString *)xxpk_vindatool {
    return [@([[XXGSecurityCheckTool sharedInstance] UVItinitse]) stringValue];
}

- (NSString *)xxpk_docPath {
    return XXGAppInfo.xxpk_applicationPath;
}

- (NSString *)xxpk_network {
    return XXGNetworkMonitor.xxpk_networkType;
}

- (NSString *)xxpk_lang {
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:yearsExpand.xxpk_lang];
    NSString *name = [array objectAtIndex:0];
    return name;
}

- (NSString *)xxpk_scale {
    return [NSString stringWithFormat:@"%.0f",UIScreen.mainScreen.scale];
}

- (NSString *)xxpk_landscape {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    BOOL IsPortrait = UIInterfaceOrientationIsPortrait([UIApplication sharedApplication].statusBarOrientation);
#pragma clang diagnostic pop
    return IsPortrait ? yearsExpand.xxpk_landscape2 : yearsExpand.xxpk_landscape1;
}

- (NSString *)xxpk_deeplink {
    return XXGPlayKitConfig.shared.xxpk_deeplink;
}

- (NSString *)xxpk_afid {
    return [XXGAppsFlyerManager xxpk_appsFlyerUID];
}
- (NSString *)xxpk_firebaseId {
    return [XXGFirebaseManager xxpk_firebaseInstanceId];
}
@end
