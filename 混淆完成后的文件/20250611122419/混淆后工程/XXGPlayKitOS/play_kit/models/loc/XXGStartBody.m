//
//  XXGStartBody.m
//  XXGPlayKit
//
//  Created by apple on 2025/2/24.
//

#import "XXGStartBody.h"
#import "XXGAppInfo.h"
#import "UICKeyChainStore.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"

@implementation XXGStartBody

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return yearsExpand.xxpk_start_body;
}

// kds
- (NSString *)xxpk_sdkName {
    return XXGPlayKitConfig.shared.xxpk_current_sdkname;
}

- (NSString *)xxpk_version {
    return yearsExpand.xxpk_version;
}

- (NSString *)xxpk_platform {
    return yearsExpand.xxpk_platform;
}

- (NSString *)xxpk_campaign {
    return yearsExpand.xxpk_campaign;
}

- (NSString *)xxpk_type {
    return yearsExpand.xxpk_app;
}

// App
- (NSString *)xxpk_appId {
    return XXGPlayKitConfig.shared.xxpk_startid;
}

- (NSString *)xxpk_appBundleId {
    if (XXGPlayKitConfig.shared.xxpk_testBundleId && XXGPlayKitConfig.shared.xxpk_testBundleId.xxpk_isNotEmpty) {
        return XXGPlayKitConfig.shared.xxpk_testBundleId;
    }
    return XXGAppInfo.xxpk_appBundleIdentifier;
}

- (NSString *)xxpk_appVersion {
    if (XXGPlayKitConfig.shared.xxpk_testAppVersion && XXGPlayKitConfig.shared.xxpk_testAppVersion.xxpk_isNotEmpty) {
        return XXGPlayKitConfig.shared.xxpk_testAppVersion;
    }
    return XXGAppInfo.xxpk_appVersion;
}

- (NSString *)xxpk_appName {
    return XXGAppInfo.xxpk_appName;
}

- (NSString *)xxpk_deviceId {
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:XXGAppInfo.xxpk_appBundleIdentifier];
    return keychain[yearsExpand.xxpk_device] ?: XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_idfa?: [[NSUUID UUID] UUIDString];
}

- (void)setXxpk_deviceId:(NSString *)xxpk_deviceId {
    UICKeyChainStore *keychain = [UICKeyChainStore keyChainStoreWithService:XXGAppInfo.xxpk_appBundleIdentifier];
    if (![xxpk_deviceId isEqualToString:keychain[yearsExpand.xxpk_device]]) {
        keychain[yearsExpand.xxpk_device] = xxpk_deviceId;
    }
}

@end

