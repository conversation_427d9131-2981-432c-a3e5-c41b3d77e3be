//
//  XXGActionItem.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/1.
//

#import "XXGActionItem.h"
#import "XXGPlayKitConfig.h"

@interface XXGActionItem()

@property (nonatomic, copy) NSString *xxpk_type;

@end

@implementation XXGActionItem

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return yearsExpand.xxpk_action_item;
}

- (XXGActionType)xxpk_action {
    // 使用静态字典保证线程安全且只初始化一次
    static NSDictionary<NSString *, NSNumber *> *actionTypeMap;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        actionTypeMap = @{
            // 基础动作类型
            yearsExpand.xxpk_open_close  : @(XXGActionTypeOpenClose),
            yearsExpand.xxpk_close       : @(XXGActionTypeClose),
            yearsExpand.xxpk_update      : @(XXGActionTypeUpdate),
            
            // H5相关类型
            yearsExpand.xxpk_trampoline  : @(XXGActionTypeTrampoline),
            yearsExpand.xxpk_splash      : @(XXGActionTypeSplash),
            
            // 用户相关类型
            yearsExpand.xxpk_mobile      : @(XXGActionTypeMobile),
            yearsExpand.xxpk_real_name   : @(XXGActionTypeRealName),
            yearsExpand.xxpk_subscribe   : @(XXGActionTypeSubscribe)
        };
    });
    
    // 安全访问并转换类型
    NSNumber *actionNumber = actionTypeMap[self.xxpk_type];
    return actionNumber ? actionNumber.unsignedIntegerValue : XXGActionTypeUnknown;
}

@end
