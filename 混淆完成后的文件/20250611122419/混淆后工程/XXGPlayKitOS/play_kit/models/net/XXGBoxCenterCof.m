//
//  XXGBoxCenterCof.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/6.
//

#import "XXGBoxCenterCof.h"
#import "XXGPlayKitConfig.h"
#import "XXGBoxManager.h"

@implementation XXGBoxCenterCof

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return yearsExpand.xxpk_box_center_cof;
}

- (NSString *)xxpk_url {
    NSString *xxpk_wenhao = [_xxpk_url containsString:yearsExpand.xxpk_vk_wenhao] ?yearsExpand.xxpk_vk_and:yearsExpand.xxpk_vk_wenhao;
    NSString *xxpk_landscape = XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_landscape;
    NSString *xxpk_lang = XXGPlayKitConfig.shared.xxpk_deviceInfo.xxpk_lang;
    NSString *xxpk_token = [XXGBoxManager xxpk_comeinedBox].xxpk_boxToken;
    NSString *xxpk_url_tmp = _xxpk_url;
// 海外拼接token
    xxpk_url_tmp = [NSString stringWithFormat:yearsExpand.xxpk_vk_roter_os,_xxpk_url,xxpk_wenhao,xxpk_landscape,xxpk_lang,xxpk_token];

    return xxpk_url_tmp;
}
@end
