//
//  XXGAdaptionCof.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/5.
//

#import "XXGAdaptionCof.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"

@implementation XXGAdaptionCof

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return yearsExpand.xxpk_adaption_cof;
}

- (NSArray<XXGSkinModel *> *)xxpk_adaption_skin_btns {
    // 使用@synchronized确保线程安全
    @synchronized (self) {
        if (!_xxpk_adaption_skin_btns) {
            // 1. 使用更清晰的变量名
            NSMutableArray<NSDictionary *> *filteredDictionaries = [NSMutableArray array];
            
            // 2. 安全遍历字典（假设self.xxpk_adaption_skin_comein是NSDictionary）
            [self.xxpk_adaption_skin_comein enumerateKeysAndObjectsUsingBlock:^(NSString *key, id _Nonnull obj, BOOL * _Nonnull stop) {
                // 3. 类型安全检查
                if (![obj isKindOfClass:[NSDictionary class]]) {
                    NSLog(@"⚠️ Expected NSDictionary for key %@, got %@", key, [obj class]);
                    return;
                }
                NSDictionary *originalDict = (NSDictionary *)obj;
                
                // 4. 创建合并后的字典（避免使用mutableCopy提升性能）
                NSMutableDictionary *combinedDict = [NSMutableDictionary dictionaryWithDictionary:originalDict];
                combinedDict[yearsExpand.xxpk_name] = key;
                
                // 5. 安全获取status值，提供默认值
                BOOL status = NO;
                if (originalDict[yearsExpand.xxpk_status] && [originalDict[yearsExpand.xxpk_status] respondsToSelector:@selector(boolValue)]) {
                    status = [originalDict[yearsExpand.xxpk_status] boolValue];
                }
                
                // 6. 条件筛选
                if (status) {
                    [filteredDictionaries addObject:[combinedDict copy]]; // 存储不可变字典
                }
            }];
            
            // 7. 批量模型转换
            _xxpk_adaption_skin_btns = [XXGSkinModel xxpk_modelArrayWithDictArray:filteredDictionaries];
        }
    }
    return _xxpk_adaption_skin_btns;
}

@end
