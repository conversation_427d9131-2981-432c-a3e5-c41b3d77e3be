//
//  XXGVKManager.m
//  XXGOSPlayKit
//
//  Created by apple on 2025/3/26.
//

#import "XXGVKManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGVKManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(yearsExpand.xxpk_middleware_vk);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        EightInfo(yearsExpand.xxpk_log_manager_vk,class?yearsExpand.xxpk_manager_status_exist:yearsExpand.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (void)xxpk_oauthOnViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_oauthOnViewController:handler:) withObject:vc withObject:handler];
    }else {
        handler(NO,@"", @"", maleKilometer.xxpk_comeinError);
    }
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xopenURL:xoptions:) withObject:application withObject:url withObject:options];
    }
    return NO;
}

+ (void)xxpk_startVKWithClientID:(NSString *)clientId clientSecret:(NSString *)clientSecret{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_startVKWithClientID:clientSecret:) withObject:clientId withObject:clientSecret];
    }
}
@end
