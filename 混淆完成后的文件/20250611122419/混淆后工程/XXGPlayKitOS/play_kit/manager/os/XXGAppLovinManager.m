//
//  XXGAppLovinManager.m
//  XXGPlayKitOS
//
//  Created by apple on 2025/5/8.
//

#import "XXGAppLovinManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGAppLovinManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(yearsExpand.xxpk_middleware_applovin);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        EightInfo(yearsExpand.xxpk_log_manager_applovin,class?[NSString stringWithFormat:yearsExpand.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:yearsExpand.xxpk_manager_status_not_exist);
    });
    if (class) {
        return [class xxpk_performSelector:@selector(shared)];
    }
    return nil;
}

+ (void)xxpk_initApplovinSDKWithKey:(NSString *)xxpk_maxkey xxpk_rewardedadid:(NSString *)xxpk_rewardedadid xxpk_testDevices:(NSArray *)xxpk_testDevices {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_initApplovinSDKWithKey:xxpk_rewardedadid:xxpk_testDevices:) withObject:xxpk_maxkey withObject:xxpk_rewardedadid withObject:xxpk_testDevices];
    }
}

+ (void)xxpk_showRewardedAdForCustomData:(nullable NSString *)customData complate:(void(^)(BOOL result))complate {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_showRewardedAdForCustomData:complate:) withObject:customData withObject:complate];
    }else {
        complate(NO);
    }
}

@end
