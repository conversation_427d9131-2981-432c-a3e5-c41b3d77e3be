//
//  XXGFirebaseManager.m
//  XXGPlayKit
//
//  Created by apple on 2025/3/22.
//

#import "XXGFirebaseManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGFirebaseManager

+ (Class)xxpk_middlewareClass {
    Class class = NSClassFromString(yearsExpand.xxpk_middleware_firebase);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        EightInfo(yearsExpand.xxpk_log_manager_firebase,class?[NSString stringWithFormat:yearsExpand.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:yearsExpand.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (void)xxpk_application:(UIApplication * _Nonnull)application xdidFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xdidFinishLaunchingWithOptions:) withObject:application withObject:launchOptions];
    }
}

+(void)xxpk_initiateOnDeviceConversionMeasurementWithPhoneNumber:(NSString *)phoneNumber {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_initiateOnDeviceConversionMeasurementWithPhoneNumber:) withObject:phoneNumber];
    }
}

+ (NSString *)xxpk_firebaseInstanceId {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_firebaseInstanceId)];
    }
    return @"";
}

/// 激活
+ (void)xxpk_logActivateWithEvent:(NSString *)event {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logActivateWithEvent:) withObject:event];
    }
}

/// 登录
+ (void)xxpk_logViewedContentEvent:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logViewedContentEvent:) withObject:uid];
    }
}

///注册
+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logCompletedRegistrationEvent:) withObject:uid];
    }
}

/// 点击支付
+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logAddedToCartEvent:withUid:) withObject:event withObject:uid];
    }
}

/// 支付
+ (void)xxpk_logPurchasedEvent:(NSString *)event xxpk_orderId:(NSString*)xxpk_orderId currency:(NSString*)currency price:(double)price {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logPurchasedEvent:xxpk_orderId:currency:price:) withObject:event withObject:xxpk_orderId withObject:currency withObject:@(price)];
    }
}

+ (void)xxpk_firebaseUniversalLogEvent:(NSString *)event params:(NSDictionary *)params withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_firebaseUniversalLogEvent:params:withUid:) withObject:event withObject:params withObject:uid];
    }
}

@end
