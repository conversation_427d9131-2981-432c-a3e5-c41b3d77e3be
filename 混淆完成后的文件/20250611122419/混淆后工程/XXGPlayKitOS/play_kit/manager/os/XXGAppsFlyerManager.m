
//  https://s3-eu-west-1.amazonaws.com/download.appsflyer.com/ios/AppsFlyerLib.framework.zip
//  https://support.appsflyer.com/hc/zh-cn/articles/115001224823-AppsFlyer-iOS-SDK%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E

#import "XXGAppsFlyerManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGAppsFlyerManager

+ (Class)xxpk_middlewareClass {
    Class class = NSClassFromString(yearsExpand.xxpk_middleware_appflyer);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        EightInfo(yearsExpand.xxpk_log_manager_appsflyer,class?[NSString stringWithFormat:yearsExpand.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:yearsExpand.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (NSString *)xxpk_appsFlyerUID {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerUID)];
    }
    return @"";
}

+ (void)xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:(NSString *)key xxpk_appleid:(NSString *)aid withActivateEvent:(NSString *)event{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:xxpk_appleid:withActivateEvent:) withObject:key withObject:aid withObject:event];
    }
}

/// 登录
+ (void)xxpk_logViewedContentEvent:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logViewedContentEvent:) withObject:uid];
    }
}

// 注册
+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)uid  {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logCompletedRegistrationEvent:) withObject:uid];
    }
}

// 点击支付
+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid  {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logAddedToCartEvent:withUid:) withObject:event withObject:uid];
    }
}

// 支付完成
+ (void)xxpk_logPurchasedEvent:(NSString *)event
                  xxpk_orderId:(NSString*)xxpk_orderId
                 currency:(NSString*)currency
                    price:(double)price {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logPurchasedEvent:xxpk_orderId:currency:price:) withObject:event withObject:xxpk_orderId withObject:currency withObject:@(price)];
    }
}

// 自定义事件
+ (void)xxpk_appsFlyerUniversalLogEvent:(NSString *)event params:(NSDictionary *)params withUid:(NSString *)uid{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerUniversalLogEvent:params:withUid:) withObject:event withObject:params withObject:uid];
    }
}

@end

