//
//  XXGFacebookManager.m
//  XXGCNPlayKit
//
//  Created by apple on 2025/3/21.
//

#import "XXGFacebookManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGFacebookManager

+ (Class)xxpk_middlewareClass {
    Class class = NSClassFromString(yearsExpand.xxpk_middleware_facebook);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        EightInfo(yearsExpand.xxpk_log_manager_facebook,class?[NSString stringWithFormat:yearsExpand.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:yearsExpand.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (void)xxpk_application:(UIApplication * _Nonnull)application xdidFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xdidFinishLaunchingWithOptions:) withObject:application withObject:launchOptions];
    }
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xopenURL:xoptions:) withObject:application withObject:url withObject:options];
    }
    return NO;
}

+ (void)xxpk_oauth:(UIViewController *)vc handler:(void(^)(NSString *userID, NSString*name, NSString*token,NSString *auth_token,NSString *nonce, NSError*error, BOOL isCancelled))handler{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_oauth:handler:) withObject:vc withObject:handler];
    }else {
        handler(nil,nil,nil,nil,nil,nil,YES);
    }
}

+ (void)xxpk_jumpToFacebookAndFollw:(NSString *)fbhome{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_jumpToFacebookAndFollw:) withObject:fbhome];
    }
}

/// 邀请好友一起游戏
+ (void)xxpk_launchFriendFinderDialogWithCompletionHandler:(void(^)(BOOL success, NSError * _Nullable error))completionHandler{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_launchFriendFinderDialogWithCompletionHandler:) withObject:completionHandler];
    }
}

+ (void)xxpk_logViewedContentEvent{

    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logViewedContentEvent)];
    }
}
+ (void)xxpk_logCompletedRegistrationEvent{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logCompletedRegistrationEvent)];
    }
}

+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logAddedToCartEvent:withUid:) withObject:event withObject:uid];
    }
}

+ (void)xxpk_logPurchasedEventOrderId:(NSString*)xxpk_orderId
                             currency:(NSString*)currency
                                price:(double)price{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logPurchasedEventOrderId:currency:price:) withObject:xxpk_orderId withObject:currency withObject:@(price)];
    }
}

+ (void)xxpk_fbUniversalLogEvent:(NSString *)eventName withUid:(NSString *)uid params:(NSDictionary *)params{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_fbUniversalLogEvent:withUid:params:) withObject:eventName withObject:uid withObject:params];
    }
}

+ (void)xxpk_sharedLinkToFacebookWithUrl:(NSString *)linkUrl withvc:(UIViewController *)vc{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sharedLinkToFacebookWithUrl:withvc:) withObject:linkUrl withObject:vc];
    }
}

+ (void)xxpk_sharedImageToFacebookWithImage:(UIImage *)image  withvc:(UIViewController *)vc{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sharedImageToFacebookWithImage:withvc:) withObject:image withObject:vc];
    }
}

+ (void)xxpk_sharedImageToFacebookWithImageUrl:(NSString *)imageUrl  withvc:(UIViewController *)vc{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_sharedImageToFacebookWithImageUrl:withvc:) withObject:imageUrl withObject:vc];
    }
}

@end
