#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一变量提取方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ObjectiveC.oc_custom import custom_core

def test_unified_constants():
    """测试统一常量提取"""
    print("=" * 60)
    print("测试统一常量提取")
    print("=" * 60)
    
    test_content = """
// 用户的具体问题
static NSMutableDictionary *resultDic;
static id decryptedJSONObject;

// 其他各种static常量
static BOOL _isEnabled = YES;
static NSString *_keyString = @"key";
static NSInteger _counter = 0;
static const id kDefaultObject = nil;
static CGFloat _height = 100.0;

// 自定义类型
static NSMutableArray *_dataArray;
static UIViewController *_currentController;
static NSUserDefaults *_userDefaults;

// extern 常量
extern NSString *const kExternalConstant;
extern const NSInteger kExternalNumber;

// #define 宏定义
#define MAX_SIZE 1024
#define APP_NAME @"TestApp"
    """
    
    print("测试内容:")
    print(test_content.strip())
    print()
    
    # 提取常量
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到的常量 ({len(constants)} 个):")
    for i, const in enumerate(constants, 1):
        print(f"  {i:2d}. {const}")
    print()
    
    # 验证关键常量
    expected = ['resultDic', 'decryptedJSONObject', '_isEnabled', '_keyString', '_counter', 
                'kDefaultObject', '_height', '_dataArray', '_currentController', '_userDefaults']
    
    found = [const for const in expected if const in constants]
    missing = [const for const in expected if const not in constants]
    
    print("关键常量验证:")
    for const in expected:
        status = "✅" if const in constants else "❌"
        print(f"  {status} {const}")
    
    if not missing:
        print("\n🎉 所有关键常量都被正确提取！")
    else:
        print(f"\n⚠️ 缺失 {len(missing)} 个常量: {missing}")
    
    success_rate = (len(found) / len(expected)) * 100 if expected else 0
    print(f"成功率: {success_rate:.1f}% ({len(found)}/{len(expected)})")

def test_unified_local_variables():
    """测试统一局部变量提取"""
    print("\n" + "=" * 60)
    print("测试统一局部变量提取")
    print("=" * 60)
    
    test_content = """
- (void)testMethod {
    // 基本类型局部变量
    NSInteger count = 0;
    CGFloat height = 100.0;
    BOOL isValid = YES;
    id object = nil;
    
    // 指针类型局部变量
    NSString *name = @"test";
    NSArray *items = @[];
    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
    UIView *view = [[UIView alloc] init];
    
    // const 局部变量
    const NSInteger maxCount = 100;
    const CGFloat defaultWidth = 320.0;
    
    // 自定义类型
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    UIViewController *controller = [[UIViewController alloc] init];
    
    // block 内的变量
    void (^testBlock)(void) = ^{
        NSString *blockString = @"block";
        NSInteger blockCount = 1;
        id blockObject = nil;
    };
    
    // 复杂初始化
    NSMutableArray *complexArray = [NSMutableArray arrayWithObjects:@"a", @"b", nil];
}
    """
    
    print("测试内容:")
    print(test_content.strip())
    print()
    
    # 提取局部变量
    local_vars = custom_core.process_local_variables_content(test_content)
    print(f"提取到的局部变量 ({len(local_vars)} 个):")
    for i, var in enumerate(local_vars, 1):
        print(f"  {i:2d}. {var}")
    print()
    
    # 验证关键变量
    expected_local = ['count', 'height', 'isValid', 'object', 'name', 'items', 'dict', 'view',
                      'maxCount', 'defaultWidth', 'defaults', 'controller', 'complexArray']
    
    found_local = [var for var in expected_local if var in local_vars]
    missing_local = [var for var in expected_local if var not in local_vars]
    
    print("关键局部变量验证:")
    for var in expected_local:
        status = "✅" if var in local_vars else "❌"
        print(f"  {status} {var}")
    
    if not missing_local:
        print("\n🎉 所有关键局部变量都被正确提取！")
    else:
        print(f"\n⚠️ 缺失 {len(missing_local)} 个变量: {missing_local}")
    
    success_rate = (len(found_local) / len(expected_local)) * 100 if expected_local else 0
    print(f"成功率: {success_rate:.1f}% ({len(found_local)}/{len(expected_local)})")

def test_mixed_extraction():
    """测试混合提取"""
    print("\n" + "=" * 60)
    print("测试混合提取（常量+局部变量）")
    print("=" * 60)
    
    test_content = """
// static 常量
static NSMutableDictionary *resultDic;
static id decryptedJSONObject;
static BOOL _globalFlag = YES;

@implementation TestClass

- (void)processData {
    // 局部变量
    NSString *localString = @"local";
    NSInteger localCount = 0;
    NSMutableArray *localArray = [[NSMutableArray alloc] init];
    
    // block
    void (^processBlock)(void) = ^{
        NSString *blockVar = @"block";
        id blockObject = nil;
    };
    
    // 更多局部变量
    UIView *containerView = [[UIView alloc] init];
    CGFloat width = 100.0;
}

+ (void)classMethod {
    static NSString *staticInMethod = @"static";
    NSString *localInClass = @"local";
}

@end
    """
    
    print("测试内容:")
    print(test_content.strip())
    print()
    
    # 分别提取
    constants = custom_core.process_constants_content(test_content)
    local_vars = custom_core.process_local_variables_content(test_content)
    
    print(f"常量 ({len(constants)} 个): {constants}")
    print(f"局部变量 ({len(local_vars)} 个): {local_vars}")
    
    # 验证分离效果
    expected_constants = ['resultDic', 'decryptedJSONObject', '_globalFlag', 'staticInMethod']
    expected_locals = ['localString', 'localCount', 'localArray', 'containerView', 'width', 'localInClass']
    
    const_found = [c for c in expected_constants if c in constants]
    local_found = [v for v in expected_locals if v in local_vars]
    
    print(f"\n常量匹配: {len(const_found)}/{len(expected_constants)} 个")
    print(f"局部变量匹配: {len(local_found)}/{len(expected_locals)} 个")
    
    # 检查是否有交叉污染
    cross_contamination = set(constants) & set(local_vars)
    if cross_contamination:
        print(f"⚠️ 交叉污染: {cross_contamination}")
    else:
        print("✅ 无交叉污染")

def main():
    """主函数"""
    test_unified_constants()
    test_unified_local_variables()
    test_mixed_extraction()

if __name__ == "__main__":
    main()
