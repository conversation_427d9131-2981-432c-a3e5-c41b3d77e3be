"""
通过遍历所有工程文件获取属性、文件夹名、枚举、代理、block、常量、方法等信息，
并赋值给oc_util.py中的class Folder()和class FileInfo()
"""
import os
import re
import yaml
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml
from ObjectiveC import oc_tool
from ObjectiveC.oc_custom import custom_core

def init():
    """
    初始化函数，扫描项目中的所有文件并提取相关信息
    """
    print("开始扫描项目文件内容...")

    # 初始化新名称列表
    initialize_new_lists()

    # 获取项目路径
    project_path = oc_util.path_mix_project
    print(f"项目路径：{project_path}")
    
    # 获取项目中的所有文件夹名
    get_project_folder_names(project_path)
    
    # 获取项目中的所有Objective-C文件
    oc_files = get_project_oc_files(project_path)
    print(f"找到 {len(oc_files)} 个Objective-C文件")
    
    # 各类型独立处理
    print("\n================ 第一阶段: 提取属性 ================")
    process_properties(oc_files)
    
    print("\n================ 第二阶段: 提取实例变量 ================")
    process_instance_variables(oc_files)
    
    print("\n================ 第三阶段: 提取常量 ================")
    process_constants(oc_files)
    
    print("\n================ 第四阶段: 提取方法 ================")
    process_methods(oc_files)
    
    print("\n================ 第五阶段: 提取枚举 ================")
    process_enums(oc_files)
    
    print("\n================ 第六阶段: 提取代理 ================")
    process_delegates(oc_files)
    
    print("\n================ 第七阶段: 提取Block ================")
    process_blocks(oc_files)
    
    print("\n================ 第八阶段: 提取局部变量 ================")
    process_local_variables(oc_files)
    
    print("文件内容扫描完成")
    
    # 显示扫描结果统计
    print_statistics()

def initialize_new_lists():
    """初始化所有需要的new_list"""
    # 确保old_list已初始化
    if not hasattr(oc_util.class_oc_file, 'property_name_old_list'):
        oc_util.class_oc_file.property_name_old_list = []
    if not hasattr(oc_util.class_oc_file, 'enum_old_list'):
        oc_util.class_oc_file.enum_old_list = []
    if not hasattr(oc_util.class_oc_file, 'block_old_list'):
        oc_util.class_oc_file.block_old_list = []
    if not hasattr(oc_util.class_oc_file, 'constant_old_list'):
        oc_util.class_oc_file.constant_old_list = []
    if not hasattr(oc_util.class_oc_file, 'delegate_old_list'):
        oc_util.class_oc_file.delegate_old_list = []
    if not hasattr(oc_util.class_oc_file, 'method_old_list'):
        oc_util.class_oc_file.method_old_list = []
    if not hasattr(oc_util.class_oc_file, 'local_variable_old_list'):
        oc_util.class_oc_file.local_variable_old_list = []
    
    # 初始化对应的new_list
    oc_util.class_oc_file.property_name_new_list = []
    oc_util.class_oc_file.enum_new_list = []
    oc_util.class_oc_file.block_new_list = []
    oc_util.class_oc_file.constant_new_list = []
    oc_util.class_oc_file.delegate_new_list = []
    oc_util.class_oc_file.method_new_list = []
    oc_util.class_oc_file.local_variable_new_list = []

def get_project_folder_names(project_path):
    """获取项目中的所有文件夹名称"""
    folder_names = []
    
    for root, dirs, _ in os.walk(project_path):
        for dir_name in dirs:
            # 忽略特定类型的文件夹
            if should_ignore_folder(dir_name):
                continue
            
            # 忽略系统生成的隐藏文件夹
            if dir_name.startswith('.'):
                continue
                
            # 忽略配置文件中指定要过滤的文件夹
            if dir_name in oc_yaml.list_folder_ignore_all:
                continue
                
            # 添加到文件夹名列表
            folder_names.append(dir_name)
    
    # 保存到全局变量
    oc_util.list_folder_name = folder_names
    print(f"找到 {len(folder_names)} 个文件夹")
    
    return folder_names

def should_ignore_folder(folder_name):
    """判断是否应该忽略此文件夹"""
    ignore_extensions = ['.framework', '.bundle', '.xcassets', '.lproj', '.xcodeproj', '.xcworkspace']
    
    for ext in ignore_extensions:
        if folder_name.endswith(ext):
            return True
    
    return False

def get_project_oc_files(project_path):
    """获取项目中的所有Objective-C源文件"""
    oc_files = []
    
    for root, dirs, files in os.walk(project_path):
        # 修改dirs，过滤掉应该被忽略的文件夹（就地修改防止遍历这些文件夹）
        dirs[:] = [d for d in dirs if not (d in oc_yaml.list_folder_ignore_all or 
                                         d.startswith('.') or
                                         any(d.endswith(ext) for ext in ['.framework', '.bundle', '.xcassets', '.lproj', '.xcodeproj', '.xcworkspace']))]
        
        for file_name in files:
            # 检查是否是OC文件
            if file_name.endswith('.m') or file_name.endswith('.h') or file_name.endswith('.mm'):
                file_path = os.path.join(root, file_name)
                
                # 检查文件路径是否在被忽略的文件夹中
                should_ignore = False
                for ignore_folder in oc_yaml.list_folder_ignore_all:
                    # 使用路径分隔符确保精确匹配文件夹名称
                    if f"/{ignore_folder}/" in file_path or file_path.endswith(f"/{ignore_folder}"):
                        should_ignore = True
                        break
                
                if should_ignore:
                    continue
                
                # 添加到OC文件列表
                oc_files.append(file_path)
                
                # 记录所有文件名，不包括扩展名
                base_name = os.path.splitext(file_name)[0]
                if base_name not in oc_util.class_oc_file.all_file_name_list:
                    oc_util.class_oc_file.all_file_name_list.append(base_name)
                    
    return oc_files

def _process_files_with_extractor(oc_files, extractor_func, item_type, count_func):
    """
    通用的文件处理函数

    Args:
        oc_files: 文件列表
        extractor_func: 提取函数
        item_type: 项目类型名称（用于日志）
        count_func: 获取处理结果数量的函数
    """
    print(f"开始处理 {len(oc_files)} 个文件的{item_type}...")
    for file_path in oc_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 调用对应的提取函数
            extractor_func(content)

        except Exception as e:
            print(f"处理文件 {file_path} 的{item_type}时出错: {str(e)}")

    print(f"{item_type}处理完成，共 {count_func()} 个")

##################################################################
##########################处理文件函数##############################
##################################################################
def process_properties(oc_files):
    """单独处理所有文件的属性"""
    _process_files_with_extractor(
        oc_files,
        extract_properties,
        "属性",
        lambda: len(oc_util.class_oc_file.property_name_old_list)
    )

def process_instance_variables(oc_files):
    """单独处理所有文件的实例变量"""
    _process_files_with_extractor(
        oc_files,
        extract_instance_variables,
        "实例变量",
        lambda: len(oc_util.class_oc_file.property_name_old_list)
    )

def process_constants(oc_files):
    """单独处理所有文件的常量"""
    _process_files_with_extractor(
        oc_files,
        extract_constants,
        "常量",
        lambda: len(oc_util.class_oc_file.constant_old_list)
    )

def process_methods(oc_files):
    """单独处理所有文件的方法"""
    _process_files_with_extractor(
        oc_files,
        extract_methods,
        "方法",
        lambda: len(oc_util.class_oc_file.method_old_list)
    )

def process_enums(oc_files):
    """单独处理所有文件的枚举"""
    _process_files_with_extractor(
        oc_files,
        extract_enums,
        "枚举",
        lambda: len(oc_util.class_oc_file.enum_old_list)
    )

def process_delegates(oc_files):
    """单独处理所有文件的代理"""
    _process_files_with_extractor(
        oc_files,
        extract_delegates,
        "代理",
        lambda: len(oc_util.class_oc_file.delegate_old_list)
    )

def process_blocks(oc_files):
    """单独处理所有文件的Block"""
    _process_files_with_extractor(
        oc_files,
        extract_blocks,
        "Block",
        lambda: len(oc_util.class_oc_file.block_old_list)
    )

def process_local_variables(oc_files):
    """单独处理所有文件的局部变量"""
    _process_files_with_extractor(
        oc_files,
        extract_local_variables,
        "局部变量",
        lambda: len(oc_util.class_oc_file.local_variable_old_list)
    )

##################################################################
##############################提取函数##############################
##################################################################
def extract_properties(content):
    """提取文件中的属性定义"""

    # 使用custom_core中的属性内容处理函数
    _, unique_properties, property_custom_accessors = custom_core.process_property_content(content)

    # 处理属性列表
    for property_name in unique_properties:
        # 如果属性名已经存在，则跳过
        if property_name in oc_util.class_oc_file.property_name_old_list:
            continue
        # 生成新的属性名
        new_property_name = random_one_new_name("属性", property_name)
        # 如果新属性名不存在，则跳过
        if len(new_property_name)==0:
            # 将new_name的setter方法加入被忽略列表中
            oc_util.list_ignore_keyword.append(f"set{property_name[0].upper() + property_name[1:]}")
            continue
        # 1. 首先是 property_name:property_name 格式
        param_style = f"{property_name}:{property_name}"
        if param_style not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(param_style)
            new_param_style = f"{new_property_name}:{new_property_name}"
            oc_util.class_oc_file.property_name_new_list.append(new_param_style)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(param_style)
            oc_util.class_oc_file.total_words.append(new_param_style)
        
        # 2. 检查自定义setter/getter
        custom_setter, custom_getter = property_custom_accessors.get(property_name, (None, None))
        if custom_setter and custom_setter not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(custom_setter)
            custom_setter_new = random_one_new_name("属性", custom_setter) or custom_setter
            oc_util.class_oc_file.property_name_new_list.append(custom_setter_new)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(custom_setter)
            oc_util.class_oc_file.total_words.append(custom_setter_new)
        
        if custom_getter and custom_getter not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(custom_getter)
            custom_getter_new = random_one_new_name("属性", custom_getter) or custom_getter
            oc_util.class_oc_file.property_name_new_list.append(custom_getter_new)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(custom_getter)
            oc_util.class_oc_file.total_words.append(custom_getter_new)
        
        # 3. 添加setter方法形式
        capitalized = property_name[0].upper() + property_name[1:]
        setter_name = f"set{capitalized}"
        if setter_name not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(setter_name)
            new_capitalized = new_property_name[0].upper() + new_property_name[1:]
            oc_util.class_oc_file.property_name_new_list.append(f"set{new_capitalized}")
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(setter_name)
            oc_util.class_oc_file.total_words.append(f"set{new_capitalized}")
        
        # 4. 添加带下划线的实例变量名 (_propertyName)
        ivar_name = f"_{property_name}"
        if ivar_name not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(ivar_name)
            oc_util.class_oc_file.property_name_new_list.append(f"_{new_property_name}")
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(ivar_name)
            oc_util.class_oc_file.total_words.append(f"_{new_property_name}")
        
        # 5. 添加属性名本身
        if property_name not in oc_util.class_oc_file.property_name_old_list:
            oc_util.class_oc_file.property_name_old_list.append(property_name)
            oc_util.class_oc_file.property_name_new_list.append(new_property_name)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(property_name)
            oc_util.class_oc_file.total_words.append(new_property_name)

def extract_instance_variables(content):
    """提取类扩展中的实例变量"""
    
    # 使用custom_core中的实例变量内容处理函数
    all_ivars = custom_core.process_instance_variables_content(content)
    
    # 处理每个实例变量
    for original_ivar in all_ivars:
        # 如果变量名是有效标识符，则添加
        if original_ivar and re.match(r'^\w+$', original_ivar):
            
            # 检查是否已经在属性处理中被处理过
            # 1. 检查实例变量本身是否已经存在
            if original_ivar in oc_util.class_oc_file.property_name_old_list:
                print(f"实例变量 {original_ivar} 已经在属性处理中被处理，跳过")
                continue
            
            # 2. 如果是带下划线的实例变量，检查对应的属性名是否已经被处理
            if original_ivar.startswith('_'):
                property_name = original_ivar[1:]  # 去掉下划线
                # 检查属性名相关的格式是否已经存在
                if (property_name in oc_util.class_oc_file.property_name_old_list or
                    f"{property_name}:{property_name}" in oc_util.class_oc_file.property_name_old_list):
                    print(f"实例变量 {original_ivar} 对应的属性 {property_name} 已经被处理，跳过")
                    continue
            
            # 3. 如果是不带下划线的实例变量，检查对应的带下划线版本是否已经被处理
            else:
                underscore_name = f"_{original_ivar}"
                if underscore_name in oc_util.class_oc_file.property_name_old_list:
                    print(f"实例变量 {original_ivar} 对应的带下划线版本 {underscore_name} 已经被处理，跳过")
                    continue
            
            # 生成新的变量名
            new_name = random_one_new_name("属性", original_ivar)
            if len(new_name) == 0:
                # 将new_name的setter方法加入被忽略列表中
                oc_util.list_ignore_keyword.append(f"set{original_ivar[0].upper() + original_ivar[1:]}")
                continue
            
            # 如果是带下划线的实例变量，并且对应的属性已经被处理，则使用相同的混淆名称
            if original_ivar.startswith('_'):
                property_name = original_ivar[1:]
                # 查找属性的混淆名称
                try:
                    prop_index = oc_util.class_oc_file.property_name_old_list.index(property_name)
                    if prop_index < len(oc_util.class_oc_file.property_name_new_list):
                        property_new_name = oc_util.class_oc_file.property_name_new_list[prop_index]
                        new_name = f"_{property_new_name}"
                        print(f"实例变量 {original_ivar} 使用对应属性 {property_name} 的混淆名称: {new_name}")
                except ValueError:
                    # 属性不在列表中，使用新生成的名称
                    pass
            
            # 直接添加原始变量名和新变量名的映射关系
            # 不需要额外处理下划线，保持原始格式
            
            # 1. 添加参数样式: 原变量名:原变量名 -> 原变量名:新变量名  
            param_style = f"{original_ivar}:{original_ivar}"
            if param_style not in oc_util.class_oc_file.property_name_old_list:
                oc_util.class_oc_file.property_name_old_list.append(param_style)
                new_param_style = f"{original_ivar}:{new_name}"
                oc_util.class_oc_file.property_name_new_list.append(new_param_style)
                # 添加到总表中
                oc_util.class_oc_file.total_words.append(param_style)
                oc_util.class_oc_file.total_words.append(new_param_style)
            
            # 2. 添加箭头样式: ->原变量名 -> ->新变量名
            arrow_style = f"->{original_ivar}"
            if arrow_style not in oc_util.class_oc_file.property_name_old_list:
                oc_util.class_oc_file.property_name_old_list.append(arrow_style)
                new_arrow_style = f"->{new_name}"
                oc_util.class_oc_file.property_name_new_list.append(new_arrow_style)
                # 添加到总表中
                oc_util.class_oc_file.total_words.append(arrow_style)
                oc_util.class_oc_file.total_words.append(new_arrow_style)
                
            # 3. 添加普通格式: 原变量名 -> 新变量名
            if original_ivar not in oc_util.class_oc_file.property_name_old_list:
                oc_util.class_oc_file.property_name_old_list.append(original_ivar)
                oc_util.class_oc_file.property_name_new_list.append(new_name)
                # 添加到总表中
                oc_util.class_oc_file.total_words.append(original_ivar)
                oc_util.class_oc_file.total_words.append(new_name)

def extract_enums(content):
    """提取文件中的枚举定义"""

    # 使用custom_core中的枚举内容处理函数
    all_enums = custom_core.process_enums_content(content)

    for enum_name in all_enums:
        if enum_name not in oc_util.class_oc_file.enum_old_list:
            # 生成新的枚举名
            new_name = random_one_new_name("枚举", enum_name)
            if len(new_name) == 0:
                continue

            oc_util.class_oc_file.enum_old_list.append(enum_name)
            oc_util.class_oc_file.enum_new_list.append(new_name)

            # 添加到总表中
            oc_util.class_oc_file.total_words.append(enum_name)
            oc_util.class_oc_file.total_words.append(new_name)

def extract_delegates(content):
    """提取文件中的代理定义"""

    # 使用custom_core中的代理内容处理函数
    all_delegates = custom_core.process_delegates_content(content)

    for delegate_name in all_delegates:
        if delegate_name not in oc_util.class_oc_file.delegate_old_list:
            # 生成新的代理名
            new_name = random_one_new_name("代理", delegate_name)
            if len(new_name) == 0:
                continue

            oc_util.class_oc_file.delegate_old_list.append(delegate_name)
            oc_util.class_oc_file.delegate_new_list.append(new_name)

            # 添加到总表中
            oc_util.class_oc_file.total_words.append(delegate_name)
            oc_util.class_oc_file.total_words.append(new_name)

def extract_blocks(content):
    """提取文件中的block类型定义"""

    # 使用custom_core中的Block内容处理函数
    all_blocks = custom_core.process_blocks_content(content)

    for block_name in all_blocks:
        if block_name not in oc_util.class_oc_file.block_old_list:
            # 生成新的Block名
            new_name = random_one_new_name("Block", block_name)
            if len(new_name) == 0:
                continue

            oc_util.class_oc_file.block_old_list.append(block_name)
            oc_util.class_oc_file.block_new_list.append(new_name)

            # 添加到总表中
            oc_util.class_oc_file.total_words.append(block_name)
            oc_util.class_oc_file.total_words.append(new_name)

def extract_constants(content):
    """提取文件中的常量定义"""

    # 使用custom_core中的常量内容处理函数
    all_constants = custom_core.process_constants_content(content)

    for constant_name in all_constants:
        if constant_name not in oc_util.class_oc_file.constant_old_list:
            # 生成新的常量名
            new_name = random_one_new_name("常量", constant_name)
            if len(new_name) == 0:
                continue

            oc_util.class_oc_file.constant_old_list.append(constant_name)
            oc_util.class_oc_file.constant_new_list.append(new_name)

            # 添加到总表中
            oc_util.class_oc_file.total_words.append(constant_name)
            oc_util.class_oc_file.total_words.append(new_name)

def extract_local_variables(content):
    """提取文件中的局部变量"""

    # 使用custom_core中的局部变量内容处理函数
    all_local_vars = custom_core.process_local_variables_content(content)

    for var_name in all_local_vars:
        if var_name not in oc_util.class_oc_file.local_variable_old_list:
            # 生成新的局部变量名
            new_name = random_one_new_name("局部变量", var_name)
            if len(new_name) == 0:
                continue

            oc_util.class_oc_file.local_variable_old_list.append(var_name)
            oc_util.class_oc_file.local_variable_new_list.append(new_name)

            # 添加到总表中
            oc_util.class_oc_file.total_words.append(var_name)
            oc_util.class_oc_file.total_words.append(new_name)

def extract_methods(content):
    """提取文件中的方法定义"""

    # 使用custom_core中的方法内容处理函数
    all_methods = custom_core.process_methods_content(content)

    for method_name in all_methods:

        # 如果方法名已经存在，则跳过
        if method_name in oc_util.class_oc_file.method_old_list:
            continue

        # 生成新的方法名
        new_method_name = random_one_new_name("方法", method_name)
        # 如果新方法名不存在，则跳过
        if len(new_method_name) == 0:
            continue

        # 如果是set开头的方法，则去method_old_list中查找对应的方法名对应method_new_list的名字生成new_method_name的setter方法
        if method_name.startswith('set') and len(method_name) > 3:
            # 去掉set前缀，并且首字母小写
            property_name = method_name[3].lower() + method_name[4:]
            if property_name in oc_util.class_oc_file.method_old_list:
                index = oc_util.class_oc_file.method_old_list.index(property_name)
                new_method_name = f"set{oc_util.class_oc_file.method_new_list[index][0].upper() + oc_util.class_oc_file.method_new_list[index][1:]}"
                print(f"方法 {method_name} 是setter方法，使用对应getter方法 {property_name} 的混淆名称: {new_method_name}")

        # 1. 首先是 method_name:method_name 格式
        param_style = f"{method_name}:{method_name}"
        if param_style not in oc_util.class_oc_file.method_old_list:
            oc_util.class_oc_file.method_old_list.append(param_style)
            new_param_style = f"{new_method_name}:{new_method_name}"
            oc_util.class_oc_file.method_new_list.append(new_param_style)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(param_style)
            oc_util.class_oc_file.total_words.append(new_param_style)

        # 2. 添加方法名本身
        if method_name not in oc_util.class_oc_file.method_old_list:
            oc_util.class_oc_file.method_old_list.append(method_name)
            oc_util.class_oc_file.method_new_list.append(new_method_name)
            # 添加到总表中
            oc_util.class_oc_file.total_words.append(method_name)
            oc_util.class_oc_file.total_words.append(new_method_name)



def random_one_new_name(describe, old_name):
    """生成一个新的随机名称"""

    if oc_tool.is_word_can_mix(describe, old_name) == False:
        return ''

    # 对方法进行特殊处理
    prefix = ''
    if describe == "方法":
        if old_name.startswith('init') and len(old_name) > 4:
            prefix = 'init'
            old_name = old_name[4:]
        
    # 生成新名称
    count = len(old_name)

    # 检查是否有需要保留的后缀
    keep_word = ""
    for word in oc_yaml.list_keep_word:
        if old_name.endswith(word):
            keep_word = word
            break

    # 调整长度
    count = count - len(keep_word)

    # 确定首字母是否大写
    isFirst = old_name[0].isupper()

    # 生成随机字符串
    new_name = oc_tool.random_one_string(count, isFirst) + keep_word

    # 检查新名称是否合法
    if not oc_tool.is_name_legal(prefix+new_name):
        return random_one_new_name(describe, old_name)

    # 添加到总关键词列表
    # oc_util.class_oc_file.total_words.append(old_name)
    # oc_util.class_oc_file.total_words.append(new_name)

    return prefix+new_name

def print_statistics():
    """打印扫描结果统计"""
    
    print("\n扫描结果统计:")
    print(f"文件名: {len(oc_util.class_oc_file.file_name_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.file_name_new_list)} 个)")

    # 检查新旧列表数量是否一致
    print(f"属性名: {len(oc_util.class_oc_file.property_name_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.property_name_new_list)} 个)")
    if len(oc_util.class_oc_file.property_name_old_list) != len(oc_util.class_oc_file.property_name_new_list):
        print(f"警告：属性新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.property_name_old_list)}, 新:{len(oc_util.class_oc_file.property_name_new_list)}")
    
    print(f"枚举: {len(oc_util.class_oc_file.enum_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.enum_new_list)} 个)")
    if len(oc_util.class_oc_file.enum_old_list) != len(oc_util.class_oc_file.enum_new_list):
        print(f"警告：枚举新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.enum_old_list)}, 新:{len(oc_util.class_oc_file.enum_new_list)}")
    
    print(f"Block: {len(oc_util.class_oc_file.block_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.block_new_list)} 个)")
    if len(oc_util.class_oc_file.block_old_list) != len(oc_util.class_oc_file.block_new_list):
        print(f"警告：Block新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.block_old_list)}, 新:{len(oc_util.class_oc_file.block_new_list)}")
    
    print(f"常量: {len(oc_util.class_oc_file.constant_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.constant_new_list)} 个)")
    if len(oc_util.class_oc_file.constant_old_list) != len(oc_util.class_oc_file.constant_new_list):
        print(f"警告：常量新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.constant_old_list)}, 新:{len(oc_util.class_oc_file.constant_new_list)}")
    
    print(f"代理: {len(oc_util.class_oc_file.delegate_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.delegate_new_list)} 个)")
    if len(oc_util.class_oc_file.delegate_old_list) != len(oc_util.class_oc_file.delegate_new_list):
        print(f"警告：代理新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.delegate_old_list)}, 新:{len(oc_util.class_oc_file.delegate_new_list)}")
    
    print(f"方法: {len(oc_util.class_oc_file.method_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.method_new_list)} 个)")
    if len(oc_util.class_oc_file.method_old_list) != len(oc_util.class_oc_file.method_new_list):
        print(f"警告：方法新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.method_old_list)}, 新:{len(oc_util.class_oc_file.method_new_list)}")
    
    print(f"局部变量: {len(oc_util.class_oc_file.local_variable_old_list)} 个 (混淆后 {len(oc_util.class_oc_file.local_variable_new_list)} 个)")
    if len(oc_util.class_oc_file.local_variable_old_list) != len(oc_util.class_oc_file.local_variable_new_list):
        print(f"警告：局部变量新旧列表数量不一致! 旧:{len(oc_util.class_oc_file.local_variable_old_list)}, 新:{len(oc_util.class_oc_file.local_variable_new_list)}")
    
    print(f"总关键词: {len(oc_util.class_oc_file.total_words)} 个")