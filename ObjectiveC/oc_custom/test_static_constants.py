#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 static 常量提取功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ObjectiveC.oc_custom import custom_core

def test_static_constants():
    """测试 static 常量提取功能"""
    print("=" * 60)
    print("测试 static 常量提取功能")
    print("=" * 60)
    
    # 用户提供的具体例子
    test_content = """
#import "XXGIAPConfig.h"
#import "XXGIAPHelpManager.h"

static BOOL _sendLog = YES;
static BOOL _enableLoading = YES;

@implementation XXGIAPConfig

// 其他代码
+ (void)setSendLog:(BOOL)sendLog {
    _sendLog = sendLog;
}

+ (BOOL)sendLog {
    return _sendLog;
}

+ (void)setEnableLoading:(BOOL)enableLoading {
    _enableLoading = enableLoading;
}

+ (BOOL)enableLoading {
    return _enableLoading;
}

@end
    """
    
    print("测试内容:")
    print(test_content.strip())
    print()
    
    # 提取常量
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到的常量 ({len(constants)} 个):")
    for i, const in enumerate(constants, 1):
        print(f"  {i:2d}. {const}")
    print()
    
    # 验证期望的常量
    expected_constants = ['_sendLog', '_enableLoading']
    
    print("常量验证:")
    missing_constants = []
    found_constants = []
    
    for expected in expected_constants:
        if expected in constants:
            print(f"  ✅ {expected}")
            found_constants.append(expected)
        else:
            print(f"  ❌ {expected} - 缺失")
            missing_constants.append(expected)
    
    # 检查额外提取的常量
    extra_constants = [const for const in constants if const not in expected_constants]
    if extra_constants:
        print(f"\n额外提取的常量 ({len(extra_constants)} 个):")
        for const in extra_constants:
            print(f"  + {const}")
    
    print(f"\n总结:")
    if not missing_constants:
        print("🎉 所有期望的 static 常量都被正确提取！")
    else:
        print(f"⚠️  缺失 {len(missing_constants)} 个常量: {missing_constants}")
    
    # 计算成功率
    success_rate = (len(found_constants) / len(expected_constants)) * 100 if expected_constants else 0
    print(f"成功率: {success_rate:.1f}% ({len(found_constants)}/{len(expected_constants)})")

def test_various_static_formats():
    """测试各种 static 常量格式"""
    print("\n" + "=" * 60)
    print("测试各种 static 常量格式")
    print("=" * 60)
    
    test_content = """
// 1. static BOOL 格式（不带 const）
static BOOL _debugMode = YES;
static BOOL _isEnabled = NO;

// 2. static const BOOL 格式
static const BOOL kDefaultEnabled = YES;
static const BOOL kDebugEnabled = NO;

// 3. static BOOL const 格式
static BOOL const kMaxRetries = 3;

// 4. static NSString * 格式
static NSString *_internalKey = @"internal_key";
static NSString * _anotherKey = @"another_key";

// 5. static const NSString * 格式
static const NSString *kConstantKey = @"constant_key";

// 6. static NSString * const 格式
static NSString * const kFinalKey = @"final_key";

// 7. static 数值类型
static NSInteger _counter = 0;
static CGFloat _defaultHeight = 44.0;
static int _maxCount = 100;

// 8. static const 数值类型
static const NSInteger kDefaultCount = 10;
static const CGFloat kDefaultWidth = 320.0;

// 9. 带下划线前缀的私有变量
static BOOL _privateFlag = YES;
static NSString *_privateString = @"private";
static NSInteger _privateCounter = 0;

// 10. 不应该被提取的（在方法内）
- (void)testMethod {
    static BOOL localStatic = YES;  // 这个应该被提取，因为我们不过滤
    NSString *localVar = @"local";  // 这个不应该被提取
}
    """
    
    print("各种格式测试内容:")
    print(test_content.strip())
    print()
    
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到的常量 ({len(constants)} 个):")
    for i, const in enumerate(constants, 1):
        print(f"  {i:2d}. {const}")
    print()
    
    # 验证各类格式
    expected_by_category = {
        'static BOOL (不带const)': ['_debugMode', '_isEnabled'],
        'static const BOOL': ['kDefaultEnabled', 'kDebugEnabled'],
        'static BOOL const': ['kMaxRetries'],
        'static NSString *': ['_internalKey', '_anotherKey'],
        'static const NSString *': ['kConstantKey'],
        'static NSString * const': ['kFinalKey'],
        'static 数值类型': ['_counter', '_defaultHeight', '_maxCount'],
        'static const 数值类型': ['kDefaultCount', 'kDefaultWidth'],
        '私有变量': ['_privateFlag', '_privateString', '_privateCounter'],
        '方法内static': ['localStatic']
    }
    
    print("分类验证:")
    total_expected = 0
    total_found = 0
    
    for category, expected_vars in expected_by_category.items():
        found_vars = [var for var in expected_vars if var in constants]
        total_expected += len(expected_vars)
        total_found += len(found_vars)
        
        print(f"  {category}: {len(found_vars)}/{len(expected_vars)} 个")
        for var in expected_vars:
            status = "✅" if var in constants else "❌"
            print(f"    {status} {var}")
    
    print(f"\n总体结果: {total_found}/{total_expected} 个常量被正确提取")
    success_rate = (total_found / total_expected) * 100 if total_expected > 0 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 static 常量提取功能工作完美！")
    elif success_rate >= 80:
        print("✅ static 常量提取功能工作良好")
    else:
        print("⚠️ static 常量提取功能需要进一步优化")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    edge_content = """
// 多个变量在一行（可能无法处理，但不应该崩溃）
static BOOL _flag1 = YES, _flag2 = NO;

// 复杂初始化
static NSString *_complexString = [NSString stringWithFormat:@"prefix_%@", @"suffix"];

// 带注释
static BOOL _flagWithComment = YES; // 这是一个标志

// 条件编译中的static变量
#ifdef DEBUG
    static BOOL _debugFlag = YES;
#else
    static BOOL _debugFlag = NO;
#endif

// 类方法中的static变量
+ (void)classMethod {
    static NSInteger _staticCounter = 0;
    _staticCounter++;
}

// 实例方法中的static变量
- (void)instanceMethod {
    static BOOL _hasInitialized = NO;
    if (!_hasInitialized) {
        _hasInitialized = YES;
    }
}
    """
    
    print("边界情况测试内容:")
    print(edge_content.strip())
    print()
    
    constants = custom_core.process_constants_content(edge_content)
    print(f"边界情况测试结果: {constants}")
    
    # 检查是否包含一些期望的变量
    expected_edge = ['_complexString', '_flagWithComment', '_debugFlag', '_staticCounter', '_hasInitialized']
    
    found = [const for const in expected_edge if const in constants]
    missing = [const for const in expected_edge if const not in constants]
    
    print(f"\n边界情况统计:")
    print(f"  找到: {len(found)}/{len(expected_edge)} 个")
    print(f"  找到的: {found}")
    if missing:
        print(f"  缺失的: {missing}")
    
    if len(found) >= len(expected_edge) * 0.8:  # 80%成功率
        print("✅ 边界情况测试通过")
    else:
        print("❌ 边界情况测试需要改进")

def main():
    """主函数"""
    test_static_constants()
    test_various_static_formats()
    test_edge_cases()

if __name__ == "__main__":
    main()
