#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前的匹配问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ObjectiveC.oc_custom import custom_core

def test_current_issue():
    """测试当前的匹配问题"""
    print("=" * 60)
    print("测试当前的匹配问题")
    print("=" * 60)
    
    test_content = """
static NSMutableDictionary *resultDic;
static id decryptedJSONObject;
    """
    
    print("测试内容:")
    print(test_content.strip())
    print()
    
    # 提取常量
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到的常量: {constants}")
    
    expected = ['resultDic', 'decryptedJSONObject']
    missing = [item for item in expected if item not in constants]
    
    if missing:
        print(f"❌ 缺失的常量: {missing}")
    else:
        print("✅ 所有常量都被正确提取")

if __name__ == "__main__":
    test_current_issue()
