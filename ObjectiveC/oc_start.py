import os, shutil, time
from ObjectiveC import oc_util
from ObjectiveC import oc_yaml
from ObjectiveC import oc_tool
from ObjectiveC.oc_custom import custom_a_framework
from ObjectiveC.oc_function import a_framework
from ObjectiveC.oc_function import b_project as project
from ObjectiveC.oc_function import c_folder
from ObjectiveC.oc_function import d_string
from ObjectiveC.oc_function import e_print_annotation
from ObjectiveC.oc_function import f_file
from ObjectiveC.oc_function import g_notification
from ObjectiveC.oc_custom import custom_h_search_file_content_message
from ObjectiveC.oc_function import h_search_file_content_message
from ObjectiveC.oc_function import i_property
from ObjectiveC.oc_function import j_enum
from ObjectiveC.oc_function import k_block
from ObjectiveC.oc_function import l_constant
from ObjectiveC.oc_function import m_delegate
from ObjectiveC.oc_function import n_method
from ObjectiveC.oc_function import o_image
from ObjectiveC.oc_function import p_uuid
from ObjectiveC.oc_function import q_location
from ObjectiveC.oc_function import s_string
from ObjectiveC.oc_function import t_host
from ObjectiveC.oc_function import v_local_variable

from ObjectiveC.oc_custom import custom_get_params
from ObjectiveC.oc_custom import custom_start
from ObjectiveC.oc_custom import custom_end
from ObjectiveC.oc_custom import custom_util

def init():
    # 初始化配置文件
    oc_util.init()
    oc_yaml.init()

    ########### 拷贝工程 ###########
    # 请输入工程的文件夹目录(只允许有一个.xcodeproj)
    input_path = str(input("请输入需要混淆的ObjectiveC工程路径:")).strip()
    # 如果输入为空默认为：/Users/<USER>/jumbo/xianxian/XXGPlayKit
    if input_path == '':
        input_path = '/Users/<USER>/jumbo/xianxian/XXGPlayKit'
    # 生成混淆目录
    current_date = time.strftime("%Y%m%d%H%M%S", time.localtime())
    # 获取当前混淆目录
    mix_path = get_current_mix_path(current_date, input_path)
    print('Objective-C本次混淆目录: ', current_date)
    oc_util.path_mix = mix_path
    oc_util.path_mix_project = mix_path + '/混淆后工程'
    # 获取当前工程名
    oc_util.name_current_project = get_current_project_name()
    print('当前工程名: ', oc_util.name_current_project)
    ########### 拷贝工程 ###########

    ########### 获取参数 ###########
    custom_get_params.get_params()
    ########### 获取参数 ###########

    ########### 准备工作 ###########
    custom_start.modify_project_name()
    ########### 准备工作 ###########

    # 读取系统framework关键词
    save_system_framework_path = os.getcwd() + '/配置文件/ObjectiveC配置文件/' + 'system_framework_keywords.txt'
    oc_util.list_system_framework_word = oc_util.get_text_lines(save_system_framework_path)

    # 读取工程中的framework关键词 封装起来的方法
    save_project_framework_path = mix_path + '/project_framework_keywords.txt'
    custom_a_framework.init_project(save_project_framework_path, 2)
    # a_framework.init_project(save_project_framework_path, 2)
    oc_util.list_project_framework_word = oc_util.get_text_lines(save_project_framework_path)

    # 初始化工具类
    oc_tool.init()

    # 选择您要使用的功能
    str_func = ''
    str_func = str_func + '0:执行全部操作\n'
    str_func = str_func + 't:执行半全部操作\n'
    str_func = str_func + '1:删除注释\n'
    str_func = str_func + '2:删除打印\n'
    str_func = str_func + '3:修改工程名\n'
    str_func = str_func + '4:修改文件夹名\n'
    str_func = str_func + '5:修改文件名\n'
    str_func = str_func + '6:修改通知名\n'
    str_func = str_func + '7:修改属性名和全局变量名和局部变量名\n'
    str_func = str_func + '8:修改枚举\n'
    str_func = str_func + '9:修改block名\n'
    str_func = str_func + 'a:修改常量名\n'
    str_func = str_func + 'b:修改delegate名\n'
    str_func = str_func + 'c:修改方法名\n'
    str_func = str_func + 'd:图片处理\n'
    str_func = str_func + 'e:修改uuid\n'
    # str_func = str_func + 'f:替换位置\n'
    # str_func = str_func + 'g:字符串分割\n'
    # str_func = str_func + 'h:更换接口和域名\n'
    str_func = str_func + 'j:资源加密等\n'
    str_func = str(input('请输入需要使用功能对应的数字或字母\n%s\n'%str_func))
    func = list(set(str_func))
    func.sort()
    # if 'h' in func:
    #     print('h:更换接口和域名开始...')
    #     t_host.init()
    #     print('h:更换接口和域名完成')
    if '0' in func:
        func = ['1','2','3','4','5','6','7','8','9','a','b','c','d','e','j']
    if 't' in func:
        func = ['1','3','4','5','6','7','8','9','a','b','c','d','e']
    if '1' in func:
        print('1删除注释开始...')
        e_print_annotation.delete_print_or_annotation(1)
        print('1删除注释完成')
    if '2' in func:
        print('2删除打印开始...')
        e_print_annotation.delete_print_or_annotation(2)
        print('2删除打印完成')
    if 'd' in func:
        print('d图片处理开始...')
        o_image.init()
        print('d图片处理完成')
    # 获取工程中字符串的关键词
    save_project_string_path = mix_path + '/project_all_string_words.txt'
    d_string.get_project_string(save_project_string_path)
    oc_util.list_string_words = oc_util.get_text_lines(save_project_string_path)
    if '3' in func:
        print('3修改工程名开始...')
        project.modify_project_name()
        print('3修改工程名完成')
    if '4' in func:
        print('4修改文件夹名开始...')
        c_folder.modify_project_folder_name()
        print('4修改文件夹名完成')
    # 查找工程信息
    # custom_h_search_file_content_message.init()
    h_search_file_content_message.init()
    if '5' in func:
        print('5修改文件名开始...')
        f_file.modify_oc_file_name()
        print('5修改文件名完成')
    if '6' in func:
        print('6修改通知名开始...')
        g_notification.modify_project_notification()
        print('6修改通知名完成')
    if '9' in func:
        print('9修改block名开始...')
        k_block.init()
        print('9修改block名完成')
    if '7' in func:
        print('7修改属性名和全局变量名开始...')
        i_property.init()
        v_local_variable.init()
        print('7修改属性名和全局变量名完成')
    if '8' in func:
        print('8修改枚举开始...')
        j_enum.init()
        print('8修改枚举完成')
    if 'a' in func:
        print('a修改常量名开始...')
        l_constant.init()
        print('a修改常量名完成')
    if 'b' in func:
        print('b修改delegate名开始...')
        m_delegate.init()
        print('b修改delegate名完成')
    if 'c' in func:
        print('c修改方法名开始...')
        n_method.init()
        print('c修改方法名完成')
    if 'e' in func:
        print('e修改uuid开始...')
        p_uuid.init()
        print('e修改uuid完成')
    # if 'f' in func:
    #     print('f替换位置开始...')
    #     q_location.init()
    #     print('f替换位置完成')
    # if 'g' in func:
    #     print('g字符串分割开始...')
    #     s_string.init()
    #     print('g字符串分割完成')
    if 'j' in func:
        print('2删除打印开始...')
        e_print_annotation.delete_print_or_annotation(2)
        print('2删除打印完成')
        # 后续完善
        custom_end.init(custom_util.sdk_cn_or_os)

def get_current_mix_path(date_dir,input_path):
    '''
    获取当前混淆目录
    '''
    if os.path.exists(input_path)==False or os.path.isdir(input_path)==False:
        input_path = input("\n输入路径有误,请确认后重新输入:")
        return get_current_mix_path(date_dir, input_path)
    new_path = os.getcwd() + "/混淆完成后的文件/" + date_dir + "/混淆后工程"
    # 复制工程到混淆目录，忽略.git文件夹
    shutil.copytree(input_path, new_path, ignore=shutil.ignore_patterns('.git', '.git*' ,'Pods', 'build', '.vscode'))
    return os.getcwd() + "/混淆完成后的文件/" + date_dir

def get_current_project_name(name=None):
    ''' 
    获取当前工程名 
    参数:
        name: 可选，指定工程名称，如果提供则直接返回
    返回:
        工程名称
    '''
    if name:
        return name
        
    p_name = ''
    path = oc_util.path_mix_project
    print(f"正在搜索项目文件，路径: {path}")
    
    xcodeproj_files = []
    for _, dirs, _ in os.walk(path, topdown=True):
        for dir_name in dirs:
            if dir_name.endswith('.xcodeproj'):
                xcodeproj_files.append(dir_name)
                project_name = os.path.splitext(dir_name)[0]
                if project_name not in oc_yaml.list_filter_project:
                    p_name = project_name
                    print(f"找到项目文件: {dir_name}, 项目名称: {p_name}")
                    break
    
    if not p_name:
        if xcodeproj_files:
            print(f"警告: 找到的所有项目文件都在过滤列表中: {xcodeproj_files}")
        else:
            print(f"错误: 在路径 {path} 中没有找到任何 .xcodeproj 文件")
            # 如果没有找到任何项目文件，可以尝试使用目录名作为项目名
            p_name = os.path.basename(path)
            print(f"使用目录名作为项目名: {p_name}")
    
    return p_name
